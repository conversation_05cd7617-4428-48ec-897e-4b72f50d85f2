#!/usr/bin/env python3
"""
Create a simple test MP3 file for testing audio playback
"""

import os
import subprocess

def create_test_mp3():
    """Create a simple test MP3 file using the downloader"""
    print("🎵 Creating test MP3 file...")
    
    # Use a simple, short YouTube video for testing
    test_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"  # Rick <PERSON> (short version)
    
    # Check if yt-dlp is available
    yt_dlp_path = None
    for path in ["yt-dlp.exe", "yt-dlp"]:
        if os.path.exists(path):
            yt_dlp_path = path
            break
    
    if not yt_dlp_path:
        print("❌ yt-dlp not found. Cannot create test MP3.")
        print("💡 You can manually add MP3 files to test the player.")
        return False
    
    try:
        print(f"📥 Downloading test audio from YouTube...")
        print("⚠️  This will download a short audio clip for testing")
        
        command = [
            yt_dlp_path,
            "-x",  # Extract audio
            "--audio-format", "mp3",
            "--audio-quality", "192k",
            "-o", "test_audio.%(ext)s",
            "--max-duration", "30",  # Limit to 30 seconds
            test_url
        ]
        
        result = subprocess.run(command, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Test MP3 file created successfully!")
            print("📁 File: test_audio.mp3")
            return True
        else:
            print(f"❌ Failed to create test MP3: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error creating test MP3: {e}")
        return False

def main():
    """Main function"""
    print("=" * 50)
    print("🎵 Test MP3 Creator")
    print("=" * 50)
    
    print("This tool will create a short test MP3 file for testing the music player.")
    print("It will download a short audio clip from YouTube.")
    
    response = input("\nDo you want to create a test MP3 file? (y/n): ")
    
    if response.lower() in ['y', 'yes']:
        if create_test_mp3():
            print("\n✅ Test MP3 created successfully!")
            print("You can now test the music player with this file.")
            print("\nTo test:")
            print("1. Run: python music_player.py")
            print("2. Click 'Add Files' and select test_audio.mp3")
            print("3. Double-click the song to play it")
        else:
            print("\n❌ Failed to create test MP3.")
            print("You can manually add MP3 files to test the player.")
    else:
        print("Skipping test MP3 creation.")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
