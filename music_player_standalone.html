<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Music Player Pro - Standalone</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .player-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            width: 100%;
            backdrop-filter: blur(10px);
        }

        .player-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .player-header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .player-header p {
            color: #666;
            font-size: 1.1em;
        }

        .now-playing {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 25px;
            text-align: center;
        }

        .song-title {
            font-size: 1.4em;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            word-break: break-word;
        }

        .song-info {
            color: #666;
            margin-bottom: 15px;
        }

        .progress-container {
            margin: 20px 0;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            cursor: pointer;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.1s ease;
        }

        .time-display {
            display: flex;
            justify-content: space-between;
            margin-top: 8px;
            font-size: 0.9em;
            color: #666;
        }

        .controls {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 15px;
            margin: 25px 0;
        }

        .control-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            color: white;
            font-size: 1.2em;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .control-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .control-btn:active {
            transform: scale(0.95);
        }

        .control-btn.play {
            width: 60px;
            height: 60px;
            font-size: 1.5em;
        }

        .control-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .volume-container {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 20px 0;
        }

        .volume-slider {
            flex: 1;
            height: 6px;
            background: #e0e0e0;
            border-radius: 3px;
            outline: none;
            cursor: pointer;
            -webkit-appearance: none;
        }

        .volume-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            cursor: pointer;
        }

        .volume-slider::-moz-range-thumb {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            cursor: pointer;
            border: none;
        }

        .file-input-container {
            margin: 25px 0;
            text-align: center;
        }

        .file-input {
            display: none;
        }

        .file-label {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            display: inline-block;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .file-label:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .playlist {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            max-height: 300px;
            overflow-y: auto;
        }

        .playlist-title {
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .clear-btn {
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 15px;
            padding: 5px 12px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s ease;
        }

        .clear-btn:hover {
            background: #c82333;
        }

        .playlist-item {
            padding: 10px;
            border-radius: 8px;
            cursor: pointer;
            transition: background 0.2s ease;
            margin-bottom: 5px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            word-break: break-word;
        }

        .playlist-item:hover {
            background: #e9ecef;
        }

        .playlist-item.active {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .playlist-empty {
            text-align: center;
            color: #999;
            font-style: italic;
            padding: 20px;
        }

        .remove-btn {
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 5px 8px;
            cursor: pointer;
            font-size: 12px;
            margin-left: 10px;
        }

        .remove-btn:hover {
            background: #c82333;
        }

        .drop-zone {
            border: 2px dashed #667eea;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin: 20px 0;
            transition: all 0.3s ease;
            color: #667eea;
        }

        .drop-zone.dragover {
            border-color: #764ba2;
            background: rgba(102, 126, 234, 0.1);
            color: #764ba2;
        }

        @media (max-width: 480px) {
            .player-container {
                padding: 20px;
            }
            
            .player-header h1 {
                font-size: 2em;
            }
            
            .controls {
                gap: 10px;
            }
            
            .control-btn {
                width: 45px;
                height: 45px;
            }
            
            .control-btn.play {
                width: 55px;
                height: 55px;
            }
        }
    </style>
</head>
<body>
    <div class="player-container">
        <div class="player-header">
            <h1>🎵 Music Player Pro</h1>
            <p>Standalone - No Server Required</p>
        </div>

        <div class="now-playing">
            <div class="song-title" id="songTitle">No song loaded</div>
            <div class="song-info" id="songInfo">Select audio files to start playing</div>
            
            <div class="progress-container">
                <div class="progress-bar" id="progressBar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="time-display">
                    <span id="currentTime">00:00</span>
                    <span id="totalTime">00:00</span>
                </div>
            </div>
        </div>

        <div class="controls">
            <button class="control-btn" id="prevBtn" title="Previous">⏮</button>
            <button class="control-btn play" id="playBtn" title="Play/Pause">▶</button>
            <button class="control-btn" id="stopBtn" title="Stop">⏹</button>
            <button class="control-btn" id="nextBtn" title="Next">⏭</button>
        </div>

        <div class="volume-container">
            <span>🔊</span>
            <input type="range" class="volume-slider" id="volumeSlider" min="0" max="100" value="70">
            <span id="volumeDisplay">70%</span>
        </div>

        <div class="file-input-container">
            <input type="file" class="file-input" id="fileInput" multiple accept="audio/*">
            <label for="fileInput" class="file-label">📁 Select Audio Files</label>
        </div>

        <div class="drop-zone" id="dropZone">
            🎵 Drag & Drop Audio Files Here
        </div>

        <div class="playlist">
            <div class="playlist-title">
                Playlist
                <button class="clear-btn" id="clearBtn" onclick="clearPlaylist()">Clear All</button>
            </div>
            <div id="playlistContainer">
                <div class="playlist-empty">No songs in playlist. Select or drag audio files!</div>
            </div>
        </div>

        <audio id="audioPlayer" preload="metadata"></audio>
    </div>

    <script>
        class MusicPlayer {
            constructor() {
                this.audio = document.getElementById('audioPlayer');
                this.playlist = [];
                this.currentIndex = 0;
                this.isPlaying = false;

                this.initializeElements();
                this.bindEvents();
                this.updateDisplay();

                console.log('🎵 Music Player initialized');
            }

            initializeElements() {
                this.songTitle = document.getElementById('songTitle');
                this.songInfo = document.getElementById('songInfo');
                this.playBtn = document.getElementById('playBtn');
                this.prevBtn = document.getElementById('prevBtn');
                this.nextBtn = document.getElementById('nextBtn');
                this.stopBtn = document.getElementById('stopBtn');
                this.progressBar = document.getElementById('progressBar');
                this.progressFill = document.getElementById('progressFill');
                this.currentTime = document.getElementById('currentTime');
                this.totalTime = document.getElementById('totalTime');
                this.volumeSlider = document.getElementById('volumeSlider');
                this.volumeDisplay = document.getElementById('volumeDisplay');
                this.fileInput = document.getElementById('fileInput');
                this.playlistContainer = document.getElementById('playlistContainer');
                this.dropZone = document.getElementById('dropZone');
            }

            bindEvents() {
                // Audio events
                this.audio.addEventListener('loadedmetadata', () => {
                    console.log('Audio metadata loaded');
                    this.updateDisplay();
                });

                this.audio.addEventListener('timeupdate', () => this.updateProgress());
                this.audio.addEventListener('ended', () => this.nextSong());
                this.audio.addEventListener('play', () => this.onPlay());
                this.audio.addEventListener('pause', () => this.onPause());

                this.audio.addEventListener('error', (e) => {
                    console.error('Audio error:', e);
                    alert('Error playing audio file. Please try a different file.');
                });

                // Control events
                this.playBtn.addEventListener('click', () => this.togglePlay());
                this.prevBtn.addEventListener('click', () => this.previousSong());
                this.nextBtn.addEventListener('click', () => this.nextSong());
                this.stopBtn.addEventListener('click', () => this.stop());

                // Progress bar
                this.progressBar.addEventListener('click', (e) => this.seek(e));

                // Volume
                this.volumeSlider.addEventListener('input', (e) => this.setVolume(e.target.value));

                // File input
                this.fileInput.addEventListener('change', (e) => this.loadFiles(e.target.files));

                // Drag and drop
                this.dropZone.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    this.dropZone.classList.add('dragover');
                });

                this.dropZone.addEventListener('dragleave', () => {
                    this.dropZone.classList.remove('dragover');
                });

                this.dropZone.addEventListener('drop', (e) => {
                    e.preventDefault();
                    this.dropZone.classList.remove('dragover');
                    const files = Array.from(e.dataTransfer.files).filter(file =>
                        file.type.startsWith('audio/'));
                    if (files.length > 0) {
                        this.loadFiles(files);
                    } else {
                        alert('Please drop audio files only!');
                    }
                });

                // Keyboard shortcuts
                document.addEventListener('keydown', (e) => {
                    if (e.target.tagName === 'INPUT') return;

                    switch(e.code) {
                        case 'Space':
                            e.preventDefault();
                            this.togglePlay();
                            break;
                        case 'ArrowLeft':
                            this.previousSong();
                            break;
                        case 'ArrowRight':
                            this.nextSong();
                            break;
                        case 'ArrowUp':
                            e.preventDefault();
                            this.changeVolume(5);
                            break;
                        case 'ArrowDown':
                            e.preventDefault();
                            this.changeVolume(-5);
                            break;
                    }
                });
            }

            loadFiles(files) {
                const audioFiles = Array.from(files).filter(file =>
                    file.type.startsWith('audio/'));

                if (audioFiles.length === 0) {
                    alert('No audio files found!');
                    return;
                }

                console.log(`Loading ${audioFiles.length} audio files`);

                audioFiles.forEach(file => {
                    this.playlist.push(file);
                });

                this.updatePlaylist();

                if (this.playlist.length === audioFiles.length) {
                    // First files added
                    this.currentIndex = 0;
                    this.loadCurrentSong();
                }

                this.songInfo.textContent = `${audioFiles.length} file(s) added to playlist`;
            }

            loadCurrentSong() {
                if (this.playlist.length === 0) return;

                const file = this.playlist[this.currentIndex];
                console.log(`Loading song: ${file.name}`);

                // Create object URL for the file
                const url = URL.createObjectURL(file);
                this.audio.src = url;

                this.songTitle.textContent = file.name;
                this.songInfo.textContent = `Track ${this.currentIndex + 1} of ${this.playlist.length}`;

                this.updatePlaylist();
            }

            togglePlay() {
                if (this.playlist.length === 0) {
                    alert('Please add audio files first!');
                    return;
                }

                if (this.isPlaying) {
                    console.log('Pausing audio');
                    this.audio.pause();
                } else {
                    console.log('Playing audio');
                    this.audio.play().catch(e => {
                        console.error('Playback failed:', e);
                        alert('Failed to play audio. Please try a different file.');
                    });
                }
            }

            stop() {
                console.log('Stopping audio');
                this.audio.pause();
                this.audio.currentTime = 0;
                this.onPause();
            }

            nextSong() {
                if (this.playlist.length === 0) return;

                this.currentIndex = (this.currentIndex + 1) % this.playlist.length;
                console.log(`Next song: index ${this.currentIndex}`);
                this.loadCurrentSong();
                if (this.isPlaying) {
                    this.audio.play();
                }
            }

            previousSong() {
                if (this.playlist.length === 0) return;

                this.currentIndex = (this.currentIndex - 1 + this.playlist.length) % this.playlist.length;
                console.log(`Previous song: index ${this.currentIndex}`);
                this.loadCurrentSong();
                if (this.isPlaying) {
                    this.audio.play();
                }
            }

            seek(e) {
                if (this.audio.duration) {
                    const rect = this.progressBar.getBoundingClientRect();
                    const percent = (e.clientX - rect.left) / rect.width;
                    this.audio.currentTime = percent * this.audio.duration;
                    console.log(`Seeking to ${percent * 100}%`);
                }
            }

            setVolume(value) {
                this.audio.volume = value / 100;
                this.volumeDisplay.textContent = value + '%';
            }

            changeVolume(delta) {
                const currentVolume = parseInt(this.volumeSlider.value);
                const newVolume = Math.max(0, Math.min(100, currentVolume + delta));
                this.volumeSlider.value = newVolume;
                this.setVolume(newVolume);
            }

            onPlay() {
                this.isPlaying = true;
                this.playBtn.textContent = '⏸';
                this.playBtn.title = 'Pause';
                console.log('Audio started playing');
            }

            onPause() {
                this.isPlaying = false;
                this.playBtn.textContent = '▶';
                this.playBtn.title = 'Play';
                console.log('Audio paused');
            }

            updateProgress() {
                if (this.audio.duration) {
                    const percent = (this.audio.currentTime / this.audio.duration) * 100;
                    this.progressFill.style.width = percent + '%';

                    this.currentTime.textContent = this.formatTime(this.audio.currentTime);
                    this.totalTime.textContent = this.formatTime(this.audio.duration);
                }
            }

            updateDisplay() {
                if (this.audio.duration) {
                    this.totalTime.textContent = this.formatTime(this.audio.duration);
                }
            }

            updatePlaylist() {
                if (this.playlist.length === 0) {
                    this.playlistContainer.innerHTML = '<div class="playlist-empty">No songs in playlist. Select or drag audio files!</div>';
                    return;
                }

                this.playlistContainer.innerHTML = '';
                this.playlist.forEach((file, index) => {
                    const item = document.createElement('div');
                    item.className = 'playlist-item' + (index === this.currentIndex ? ' active' : '');

                    const songName = document.createElement('span');
                    songName.textContent = file.name;
                    songName.style.flex = '1';

                    const removeBtn = document.createElement('button');
                    removeBtn.className = 'remove-btn';
                    removeBtn.textContent = '✕';
                    removeBtn.title = 'Remove from playlist';
                    removeBtn.onclick = (e) => {
                        e.stopPropagation();
                        this.removeFromPlaylist(index);
                    };

                    item.appendChild(songName);
                    item.appendChild(removeBtn);

                    item.addEventListener('click', () => {
                        this.currentIndex = index;
                        this.loadCurrentSong();
                        if (this.isPlaying) {
                            this.audio.play();
                        }
                    });

                    this.playlistContainer.appendChild(item);
                });
            }

            removeFromPlaylist(index) {
                console.log(`Removing song at index ${index}`);
                this.playlist.splice(index, 1);

                if (this.currentIndex >= this.playlist.length) {
                    this.currentIndex = Math.max(0, this.playlist.length - 1);
                }

                this.updatePlaylist();

                if (this.playlist.length > 0) {
                    this.loadCurrentSong();
                } else {
                    this.songTitle.textContent = 'No song loaded';
                    this.songInfo.textContent = 'Select or drag audio files!';
                    this.audio.src = '';
                }
            }

            formatTime(seconds) {
                if (isNaN(seconds)) return '00:00';
                const mins = Math.floor(seconds / 60);
                const secs = Math.floor(seconds % 60);
                return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            }
        }

        // Clear playlist function
        function clearPlaylist() {
            if (confirm('Clear all songs from playlist?')) {
                musicPlayer.playlist = [];
                musicPlayer.currentIndex = 0;
                musicPlayer.updatePlaylist();
                musicPlayer.songTitle.textContent = 'No song loaded';
                musicPlayer.songInfo.textContent = 'Select or drag audio files!';
                musicPlayer.audio.src = '';
                musicPlayer.onPause();
                console.log('Playlist cleared');
            }
        }

        // Initialize the music player when the page loads
        let musicPlayer;
        document.addEventListener('DOMContentLoaded', () => {
            musicPlayer = new MusicPlayer();
            console.log('🎵 Music Player ready!');
        });
    </script>
</body>
</html>
