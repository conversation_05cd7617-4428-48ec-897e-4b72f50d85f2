import json
import os
from datetime import datetime

class PlaylistManager:
    def __init__(self):
        self.playlists = {}
        self.current_playlist = []
        self.current_index = 0
        self.playlists_dir = "playlists"
        self.ensure_playlists_directory()
        self.load_all_playlists()
    
    def ensure_playlists_directory(self):
        """Create playlists directory if it doesn't exist"""
        if not os.path.exists(self.playlists_dir):
            os.makedirs(self.playlists_dir)
    
    def create_playlist(self, name, songs=None):
        """Create a new playlist"""
        if songs is None:
            songs = []
        
        playlist_data = {
            'name': name,
            'songs': songs,
            'created': datetime.now().isoformat(),
            'modified': datetime.now().isoformat()
        }
        
        self.playlists[name] = playlist_data
        self.save_playlist(name)
        return True
    
    def add_song_to_playlist(self, playlist_name, song_path):
        """Add a song to a playlist"""
        if playlist_name in self.playlists:
            if song_path not in self.playlists[playlist_name]['songs']:
                self.playlists[playlist_name]['songs'].append(song_path)
                self.playlists[playlist_name]['modified'] = datetime.now().isoformat()
                self.save_playlist(playlist_name)
                return True
        return False
    
    def remove_song_from_playlist(self, playlist_name, song_path):
        """Remove a song from a playlist"""
        if playlist_name in self.playlists:
            if song_path in self.playlists[playlist_name]['songs']:
                self.playlists[playlist_name]['songs'].remove(song_path)
                self.playlists[playlist_name]['modified'] = datetime.now().isoformat()
                self.save_playlist(playlist_name)
                return True
        return False
    
    def delete_playlist(self, playlist_name):
        """Delete a playlist"""
        if playlist_name in self.playlists:
            del self.playlists[playlist_name]
            playlist_file = os.path.join(self.playlists_dir, f"{playlist_name}.json")
            if os.path.exists(playlist_file):
                os.remove(playlist_file)
            return True
        return False
    
    def get_playlist(self, playlist_name):
        """Get a playlist by name"""
        return self.playlists.get(playlist_name)
    
    def get_all_playlists(self):
        """Get all playlists"""
        return self.playlists
    
    def save_playlist(self, playlist_name):
        """Save a playlist to file"""
        if playlist_name in self.playlists:
            playlist_file = os.path.join(self.playlists_dir, f"{playlist_name}.json")
            try:
                with open(playlist_file, 'w', encoding='utf-8') as f:
                    json.dump(self.playlists[playlist_name], f, indent=2, ensure_ascii=False)
                return True
            except Exception as e:
                print(f"Error saving playlist {playlist_name}: {e}")
                return False
        return False
    
    def load_playlist(self, playlist_name):
        """Load a playlist from file"""
        playlist_file = os.path.join(self.playlists_dir, f"{playlist_name}.json")
        try:
            if os.path.exists(playlist_file):
                with open(playlist_file, 'r', encoding='utf-8') as f:
                    playlist_data = json.load(f)
                    self.playlists[playlist_name] = playlist_data
                    return True
        except Exception as e:
            print(f"Error loading playlist {playlist_name}: {e}")
        return False
    
    def load_all_playlists(self):
        """Load all playlists from the playlists directory"""
        if os.path.exists(self.playlists_dir):
            for filename in os.listdir(self.playlists_dir):
                if filename.endswith('.json'):
                    playlist_name = filename[:-5]  # Remove .json extension
                    self.load_playlist(playlist_name)
    
    def set_current_playlist(self, songs, index=0):
        """Set the current playing playlist"""
        self.current_playlist = songs
        self.current_index = max(0, min(index, len(songs) - 1))
    
    def get_current_song(self):
        """Get the current song in the playlist"""
        if 0 <= self.current_index < len(self.current_playlist):
            return self.current_playlist[self.current_index]
        return None
    
    def next_song(self):
        """Move to the next song in the playlist"""
        if self.current_playlist:
            self.current_index = (self.current_index + 1) % len(self.current_playlist)
            return self.get_current_song()
        return None
    
    def previous_song(self):
        """Move to the previous song in the playlist"""
        if self.current_playlist:
            self.current_index = (self.current_index - 1) % len(self.current_playlist)
            return self.get_current_song()
        return None
    
    def shuffle_playlist(self):
        """Shuffle the current playlist"""
        import random
        if self.current_playlist:
            current_song = self.get_current_song()
            random.shuffle(self.current_playlist)
            # Keep the current song at the current index
            if current_song and current_song in self.current_playlist:
                old_index = self.current_playlist.index(current_song)
                self.current_playlist[old_index], self.current_playlist[self.current_index] = \
                    self.current_playlist[self.current_index], self.current_playlist[old_index]
    
    def get_playlist_names(self):
        """Get list of all playlist names"""
        return list(self.playlists.keys())
    
    def export_playlist(self, playlist_name, export_path):
        """Export a playlist to a file"""
        if playlist_name in self.playlists:
            try:
                with open(export_path, 'w', encoding='utf-8') as f:
                    json.dump(self.playlists[playlist_name], f, indent=2, ensure_ascii=False)
                return True
            except Exception as e:
                print(f"Error exporting playlist: {e}")
        return False
    
    def import_playlist(self, import_path):
        """Import a playlist from a file"""
        try:
            with open(import_path, 'r', encoding='utf-8') as f:
                playlist_data = json.load(f)
                playlist_name = playlist_data.get('name', 'Imported Playlist')
                
                # Ensure unique name
                original_name = playlist_name
                counter = 1
                while playlist_name in self.playlists:
                    playlist_name = f"{original_name} ({counter})"
                    counter += 1
                
                playlist_data['name'] = playlist_name
                self.playlists[playlist_name] = playlist_data
                self.save_playlist(playlist_name)
                return playlist_name
        except Exception as e:
            print(f"Error importing playlist: {e}")
        return None
