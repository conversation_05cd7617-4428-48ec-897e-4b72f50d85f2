#!/usr/bin/env python3
"""
Audio Diagnostics Tool for Music Player Pro
This tool helps diagnose audio playback issues
"""

import pygame
import sys
import os
import time

def test_pygame_audio():
    """Test pygame audio system"""
    print("🔊 Testing pygame audio system...")
    
    try:
        # Test different pygame mixer configurations
        configs = [
            {"frequency": 44100, "size": -16, "channels": 2, "buffer": 1024},
            {"frequency": 22050, "size": -16, "channels": 2, "buffer": 512},
            {"frequency": 44100, "size": -16, "channels": 1, "buffer": 1024},
            {"frequency": 22050, "size": -16, "channels": 1, "buffer": 512},
        ]
        
        for i, config in enumerate(configs):
            try:
                print(f"\n  Testing config {i+1}: {config}")
                pygame.mixer.quit()  # Clean shutdown
                pygame.mixer.pre_init(**config)
                pygame.mixer.init()
                
                print(f"    ✓ Mixer initialized successfully")
                print(f"    ✓ Frequency: {pygame.mixer.get_init()[0]} Hz")
                print(f"    ✓ Channels: {pygame.mixer.get_init()[2]}")
                print(f"    ✓ Buffer size: {config['buffer']}")
                
                # Test if we can get mixer info
                mixer_info = pygame.mixer.get_init()
                if mixer_info:
                    print(f"    ✓ Mixer active: {mixer_info}")
                    return True
                else:
                    print(f"    ✗ Mixer not active")
                    
            except pygame.error as e:
                print(f"    ✗ Config {i+1} failed: {e}")
                continue
        
        return False
        
    except Exception as e:
        print(f"  ✗ Pygame audio test failed: {e}")
        return False

def test_audio_file_loading():
    """Test loading different audio file types"""
    print("\n🎵 Testing audio file loading...")
    
    # Create a simple test tone (if no audio files available)
    try:
        pygame.mixer.init()
        
        # Test if we can create and play a simple sound
        print("  Creating test tone...")
        
        # Generate a simple sine wave tone
        import numpy as np
        duration = 1.0  # seconds
        sample_rate = 22050
        frequency = 440  # A4 note
        
        frames = int(duration * sample_rate)
        arr = np.zeros((frames, 2))
        
        for i in range(frames):
            wave = np.sin(2 * np.pi * frequency * i / sample_rate)
            arr[i][0] = wave * 0.3  # Left channel
            arr[i][1] = wave * 0.3  # Right channel
        
        # Convert to pygame sound
        sound_array = (arr * 32767).astype(np.int16)
        sound = pygame.sndarray.make_sound(sound_array)
        
        print("  ✓ Test tone created successfully")
        print("  🔊 Playing test tone for 1 second...")
        
        sound.play()
        time.sleep(1.1)  # Wait for sound to finish
        
        print("  ✓ Test tone playback completed")
        return True
        
    except ImportError:
        print("  ⚠️  NumPy not available for test tone generation")
        print("  Testing with pygame music module instead...")
        
        try:
            # Test basic pygame music functionality
            print("  Testing pygame.mixer.music module...")
            
            # Check if we can access music functions
            pygame.mixer.music.set_volume(0.7)
            volume = pygame.mixer.music.get_volume()
            print(f"  ✓ Music module accessible, volume: {volume}")
            
            return True
            
        except Exception as e:
            print(f"  ✗ Music module test failed: {e}")
            return False
            
    except Exception as e:
        print(f"  ✗ Audio file loading test failed: {e}")
        return False

def test_system_audio():
    """Test system audio configuration"""
    print("\n🖥️  Testing system audio configuration...")
    
    try:
        # Check pygame audio drivers
        print("  Available audio drivers:")
        drivers = pygame.mixer.get_init()
        if drivers:
            print(f"    Current driver: {drivers}")
        
        # Try to get system audio info
        import platform
        system = platform.system()
        print(f"  Operating system: {system}")
        
        if system == "Windows":
            print("  Windows audio tips:")
            print("    - Check Windows volume mixer")
            print("    - Ensure default audio device is set correctly")
            print("    - Try running as administrator if needed")
            
        elif system == "Linux":
            print("  Linux audio tips:")
            print("    - Check ALSA/PulseAudio configuration")
            print("    - Try: sudo apt-get install python3-pygame")
            
        elif system == "Darwin":  # macOS
            print("  macOS audio tips:")
            print("    - Check System Preferences > Sound")
            print("    - Ensure Python has microphone/audio permissions")
        
        return True
        
    except Exception as e:
        print(f"  ✗ System audio test failed: {e}")
        return False

def test_volume_levels():
    """Test volume configuration"""
    print("\n🔊 Testing volume levels...")
    
    try:
        pygame.mixer.init()
        
        # Test mixer volume
        pygame.mixer.music.set_volume(1.0)
        volume = pygame.mixer.music.get_volume()
        print(f"  Mixer volume: {volume} (should be 1.0)")
        
        # Test different volume levels
        test_volumes = [0.1, 0.5, 0.7, 1.0]
        for vol in test_volumes:
            pygame.mixer.music.set_volume(vol)
            actual_vol = pygame.mixer.music.get_volume()
            print(f"  Set volume {vol}, got {actual_vol}")
        
        return True
        
    except Exception as e:
        print(f"  ✗ Volume test failed: {e}")
        return False

def main():
    """Run all audio diagnostics"""
    print("=" * 60)
    print("🔧 Music Player Pro - Audio Diagnostics")
    print("=" * 60)
    
    results = []
    
    # Run all tests
    results.append(("Pygame Audio System", test_pygame_audio()))
    results.append(("Audio File Loading", test_audio_file_loading()))
    results.append(("System Audio Config", test_system_audio()))
    results.append(("Volume Levels", test_volume_levels()))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 DIAGNOSTIC SUMMARY")
    print("=" * 60)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {test_name}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("✅ All audio tests passed!")
        print("If you're still not hearing audio, check:")
        print("1. System volume levels")
        print("2. Default audio output device")
        print("3. Audio file formats (try MP3 or WAV)")
    else:
        print("❌ Some audio tests failed!")
        print("This indicates audio system issues that need to be resolved.")
    
    print("\n🔧 RECOMMENDED FIXES:")
    print("1. Try installing/reinstalling pygame: pip install --upgrade pygame")
    print("2. Check system audio settings and default output device")
    print("3. Test with different audio file formats")
    print("4. Restart the application and try again")
    print("5. On Windows: Check Windows volume mixer")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
