#!/usr/bin/env python3
"""
Simple Music Player - Streamlined version with essential controls only
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import threading
import time
from audio_player import AudioPlayer
from playlist_manager import PlaylistManager

class SimpleMusicPlayer:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Simple Music Player")
        self.root.geometry("600x400")
        self.root.minsize(500, 300)
        
        # Initialize audio player
        self.audio_player = AudioPlayer()
        self.playlist_manager = PlaylistManager()
        
        # Variables
        self.current_song_var = tk.StringVar(value="No song loaded")
        self.time_var = tk.StringVar(value="00:00 / 00:00")
        self.volume_var = tk.DoubleVar(value=70)
        self.position_var = tk.DoubleVar(value=0)
        self.is_seeking = False
        
        # Current playlist (simple list)
        self.current_playlist = []
        self.current_index = 0
        
        self.create_widgets()
        self.start_update_thread()
    
    def create_widgets(self):
        """Create the simplified GUI"""
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Current song display
        song_frame = ttk.LabelFrame(main_frame, text="Now Playing", padding="10")
        song_frame.pack(fill=tk.X, pady=(0, 10))
        
        song_label = ttk.Label(song_frame, textvariable=self.current_song_var, 
                              font=('Arial', 12, 'bold'))
        song_label.pack(anchor=tk.W)
        
        time_label = ttk.Label(song_frame, textvariable=self.time_var)
        time_label.pack(anchor=tk.W)
        
        # Progress bar
        self.progress_scale = ttk.Scale(song_frame, from_=0, to=100, 
                                       variable=self.position_var, orient=tk.HORIZONTAL)
        self.progress_scale.pack(fill=tk.X, pady=(5, 0))
        self.progress_scale.bind("<Button-1>", self.on_seek_start)
        self.progress_scale.bind("<ButtonRelease-1>", self.on_seek_end)
        
        # Controls frame
        controls_frame = ttk.Frame(main_frame)
        controls_frame.pack(fill=tk.X, pady=(0, 10))
        
        # File controls
        file_frame = ttk.Frame(controls_frame)
        file_frame.pack(side=tk.LEFT)
        
        ttk.Button(file_frame, text="Open File", command=self.open_file).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(file_frame, text="Open Folder", command=self.open_folder).pack(side=tk.LEFT, padx=5)
        
        # Playback controls
        playback_frame = ttk.Frame(controls_frame)
        playback_frame.pack(side=tk.LEFT, padx=(20, 0))
        
        self.prev_btn = ttk.Button(playback_frame, text="⏮", command=self.previous_song, width=3)
        self.prev_btn.pack(side=tk.LEFT, padx=2)
        
        self.play_btn = ttk.Button(playback_frame, text="▶", command=self.toggle_play, width=3)
        self.play_btn.pack(side=tk.LEFT, padx=2)
        
        self.stop_btn = ttk.Button(playback_frame, text="⏹", command=self.stop_song, width=3)
        self.stop_btn.pack(side=tk.LEFT, padx=2)
        
        self.next_btn = ttk.Button(playback_frame, text="⏭", command=self.next_song, width=3)
        self.next_btn.pack(side=tk.LEFT, padx=2)
        
        # Volume control
        volume_frame = ttk.Frame(controls_frame)
        volume_frame.pack(side=tk.RIGHT)
        
        ttk.Label(volume_frame, text="🔊").pack(side=tk.LEFT)
        volume_scale = ttk.Scale(volume_frame, from_=0, to=100, variable=self.volume_var,
                               orient=tk.HORIZONTAL, length=100, command=self.on_volume_change)
        volume_scale.pack(side=tk.LEFT, padx=(5, 0))
        
        # Playlist frame
        playlist_frame = ttk.LabelFrame(main_frame, text="Playlist", padding="5")
        playlist_frame.pack(fill=tk.BOTH, expand=True)
        
        # Playlist listbox
        list_frame = ttk.Frame(playlist_frame)
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        self.playlist_listbox = tk.Listbox(list_frame, selectmode=tk.SINGLE)
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.playlist_listbox.yview)
        self.playlist_listbox.configure(yscrollcommand=scrollbar.set)
        
        self.playlist_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Playlist controls
        playlist_controls = ttk.Frame(playlist_frame)
        playlist_controls.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Button(playlist_controls, text="Clear", command=self.clear_playlist).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(playlist_controls, text="Remove", command=self.remove_selected).pack(side=tk.LEFT, padx=5)
        
        # Bind double-click to play
        self.playlist_listbox.bind('<Double-1>', self.on_playlist_double_click)
        
        # Keyboard shortcuts
        self.root.bind('<space>', lambda e: self.toggle_play())
        self.root.bind('<Left>', lambda e: self.previous_song())
        self.root.bind('<Right>', lambda e: self.next_song())
    
    def open_file(self):
        """Open a single audio file"""
        files = filedialog.askopenfilenames(
            title="Select Audio Files",
            filetypes=[
                ("Audio Files", "*.mp3 *.wav *.ogg *.flac *.m4a *.webm"),
                ("MP3 Files", "*.mp3"),
                ("WAV Files", "*.wav"),
                ("All Files", "*.*")
            ]
        )
        
        if files:
            for file_path in files:
                self.add_to_playlist(file_path)
    
    def open_folder(self):
        """Open all audio files from a folder"""
        folder = filedialog.askdirectory(title="Select Music Folder")
        if folder:
            audio_extensions = ('.mp3', '.wav', '.ogg', '.flac', '.m4a', '.webm')
            for root, _, files in os.walk(folder):
                for file in files:
                    if file.lower().endswith(audio_extensions):
                        file_path = os.path.join(root, file)
                        self.add_to_playlist(file_path)
    
    def add_to_playlist(self, file_path):
        """Add a file to the playlist"""
        if file_path not in self.current_playlist:
            self.current_playlist.append(file_path)
            filename = os.path.basename(file_path)
            self.playlist_listbox.insert(tk.END, filename)
    
    def clear_playlist(self):
        """Clear the entire playlist"""
        self.current_playlist.clear()
        self.playlist_listbox.delete(0, tk.END)
        self.current_index = 0
    
    def remove_selected(self):
        """Remove selected item from playlist"""
        selection = self.playlist_listbox.curselection()
        if selection:
            index = selection[0]
            self.playlist_listbox.delete(index)
            del self.current_playlist[index]
            if self.current_index >= len(self.current_playlist):
                self.current_index = max(0, len(self.current_playlist) - 1)
    
    def on_playlist_double_click(self, event):
        """Handle double-click on playlist item"""
        selection = self.playlist_listbox.curselection()
        if selection:
            self.current_index = selection[0]
            self.load_and_play_current()
    
    def load_and_play_current(self):
        """Load and play the current song"""
        if 0 <= self.current_index < len(self.current_playlist):
            file_path = self.current_playlist[self.current_index]
            if self.audio_player.load_song(file_path):
                filename = os.path.basename(file_path)
                self.current_song_var.set(filename)
                self.audio_player.play()
                self.play_btn.config(text="⏸")
                
                # Highlight current song in playlist
                self.playlist_listbox.selection_clear(0, tk.END)
                self.playlist_listbox.selection_set(self.current_index)
                self.playlist_listbox.see(self.current_index)
            else:
                self.show_playback_error(file_path)
    
    def show_playback_error(self, file_path):
        """Show error message for playback issues"""
        filename = os.path.basename(file_path)
        file_ext = os.path.splitext(file_path)[1].lower()
        
        if file_ext == '.webm':
            error_msg = f"Cannot play: {filename}\n\nWebM files may use unsupported audio codecs.\nTry converting to MP3 format."
        elif file_ext in ['.m4a', '.flac']:
            error_msg = f"Cannot play: {filename}\n\nThis format may not be supported.\nTry converting to MP3 or WAV format."
        else:
            error_msg = f"Cannot play: {filename}\n\nFile may be corrupted or unsupported.\nTry using MP3 or WAV files."
        
        messagebox.showerror("Playback Error", error_msg)
    
    def toggle_play(self):
        """Toggle play/pause"""
        if self.audio_player.is_playing:
            self.audio_player.pause()
            self.play_btn.config(text="▶")
        else:
            if self.audio_player.current_song:
                self.audio_player.play()
                self.play_btn.config(text="⏸")
            else:
                # No song loaded, try to play first song
                if self.current_playlist:
                    self.current_index = 0
                    self.load_and_play_current()
    
    def stop_song(self):
        """Stop current song"""
        self.audio_player.stop()
        self.play_btn.config(text="▶")
        self.position_var.set(0)
    
    def next_song(self):
        """Play next song"""
        if self.current_playlist and self.current_index < len(self.current_playlist) - 1:
            self.current_index += 1
            self.load_and_play_current()
    
    def previous_song(self):
        """Play previous song"""
        if self.current_playlist and self.current_index > 0:
            self.current_index -= 1
            self.load_and_play_current()
    
    def on_volume_change(self, value):
        """Handle volume change"""
        volume = float(value) / 100.0
        self.audio_player.set_volume(volume)
    
    def on_seek_start(self, event):
        """Handle seek start"""
        self.is_seeking = True
    
    def on_seek_end(self, event):
        """Handle seek end"""
        if self.audio_player.current_song:
            position = self.position_var.get() / 100.0 * self.audio_player.get_duration()
            self.audio_player.set_position(position)
        self.is_seeking = False
    
    def format_time(self, seconds):
        """Format time in MM:SS format"""
        if seconds < 0:
            seconds = 0
        minutes = int(seconds // 60)
        seconds = int(seconds % 60)
        return f"{minutes:02d}:{seconds:02d}"
    
    def start_update_thread(self):
        """Start the GUI update thread"""
        def update_gui():
            while True:
                try:
                    if not self.is_seeking and self.audio_player.current_song:
                        position = self.audio_player.get_position()
                        duration = self.audio_player.get_duration()
                        
                        if duration > 0:
                            progress = (position / duration) * 100
                            self.position_var.set(progress)
                        
                        time_str = f"{self.format_time(position)} / {self.format_time(duration)}"
                        self.time_var.set(time_str)
                        
                        # Check if song ended
                        if not self.audio_player.is_song_playing() and self.audio_player.is_playing:
                            self.audio_player.is_playing = False
                            self.play_btn.config(text="▶")
                            # Auto-play next song
                            self.next_song()
                    
                    time.sleep(0.1)
                except:
                    break
        
        update_thread = threading.Thread(target=update_gui)
        update_thread.daemon = True
        update_thread.start()
    
    def run(self):
        """Start the application"""
        self.root.mainloop()

def main():
    """Main function"""
    try:
        print("🎵 Starting Simple Music Player...")
        app = SimpleMusicPlayer()
        app.run()
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
