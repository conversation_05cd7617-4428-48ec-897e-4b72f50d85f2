# Music Player Pro 🎵

A simple, reliable music player with dual-mode support: Python desktop app and HTML web player for universal compatibility.

## 🚀 **Dual-Mode Music Player**

### 🖥️ **Python Desktop Version**
- **Native Performance**: Fast, responsive desktop application
- **Full Audio Control**: pygame-powered audio engine
- **Simple Interface**: Clean, focused design without clutter
- **Playlist Support**: Add files and folders, manage playback queue
- **Keyboard Shortcuts**: Space (play/pause), arrows (next/prev)

### 🌐 **HTML Web Version**
- **Universal Compatibility**: Works in any modern web browser
- **No Installation**: Just open the HTML file
- **Drag & Drop**: Easy file loading
- **All Audio Formats**: Supports whatever your browser supports
- **Responsive Design**: Works on desktop, tablet, and mobile

### 🎵 **Core Features (Both Versions)**
- ▶️ **Essential Controls**: Play, pause, stop, next, previous
- 🔊 **Volume Control**: Adjustable volume slider
- ⏱️ **Progress Tracking**: Seek bar and time display
- 📋 **Simple Playlist**: Add multiple files, click to play
- ⌨️ **Keyboard Shortcuts**: Space, arrow keys
- 🎯 **Format Support**: MP3, WAV, OGG, FLAC, M4A, WebM*

*WebM support varies by codec and platform

## 🚀 **Quick Start**

### 🎯 **Automatic Launcher (Recommended)**
```bash
python app.py
```
The launcher will:
1. Install required dependencies automatically
2. Test the Python audio system
3. Launch the best available version
4. Fall back to HTML if Python fails

### 🖥️ **Python Version Only**
```bash
python simple_music_player.py
```

### 🌐 **HTML Version Only**
1. Open `music_player.html` in any web browser
2. Or double-click the HTML file
3. Start adding audio files and enjoy!

### 📦 **Manual Dependencies** (if needed)
```bash
pip install pygame mutagen yt-dlp Pillow
```

## Usage

### Getting Started
1. Launch the application by running `app.py`
2. Add music files using "Add Files" or "Add Folder" buttons
3. Double-click any song to start playing
4. Use the playback controls to manage playback

### Creating Playlists
1. Go to Playlist menu → New Playlist
2. Enter a name for your playlist
3. Add songs by selecting them in the library
4. Save your playlist for future use

### Downloading Music
1. Go to Tools menu → Video to MP3 Downloader
2. Enter a video URL (YouTube, etc.)
3. Choose output directory and quality
4. Click Download to start the process

### Keyboard Shortcuts
- **Space**: Play/Pause
- **Left Arrow**: Previous song
- **Right Arrow**: Next song

## File Structure

```
videomp3/
├── app.py                 # Main application launcher
├── music_player.py        # Main GUI application
├── audio_player.py        # Audio playback engine
├── playlist_manager.py    # Playlist management
├── downloader_gui.py      # Video downloader interface
├── requirements.txt       # Python dependencies
├── playlists/            # Saved playlists directory
├── yt-dlp.exe           # yt-dlp executable (Windows)
└── README.md            # This file
```

## Dependencies

- **pygame**: Audio playback engine
- **mutagen**: Audio metadata extraction
- **yt-dlp**: Video downloading and conversion
- **Pillow**: Image processing (for future album art support)
- **tkinter**: GUI framework (included with Python)

## Supported Audio Formats

- MP3 (.mp3)
- WAV (.wav)
- OGG (.ogg)
- FLAC (.flac)
- M4A (.m4a)
- WebM (.webm)

## Troubleshooting

### Audio Playback Issues
- Ensure pygame is properly installed
- Check that audio files are not corrupted
- Verify file format is supported
- **WebM files**: Only WebM files with Vorbis audio codec are supported. VP8/VP9 video with Opus audio may not work.

### Download Issues
- Ensure yt-dlp.exe is in the application directory
- Check internet connection
- Verify the video URL is valid and accessible

### Performance Issues
- Close other audio applications
- Ensure sufficient disk space for downloads
- Check system audio settings

## Contributing

Feel free to contribute to this project by:
- Reporting bugs
- Suggesting new features
- Submitting pull requests
- Improving documentation

## License

This project is open source and available under the MIT License.

## Acknowledgments

- Built with Python and tkinter
- Audio playback powered by pygame
- Video downloading powered by yt-dlp
- Metadata extraction using mutagen

---

**Music Player Pro** - Enjoy your music! 🎵
