# Music Player Pro 🎵

A comprehensive Windows music player application with playlist management and video-to-MP3 downloading capabilities.

## Features

### 🎵 Music Playback
- Support for multiple audio formats: MP3, WAV, OGG, FLAC, M4A, WebM
- Play, pause, stop, next, previous controls
- Volume control with slider
- Seek bar for position control
- Real-time playback position display

### 📋 Playlist Management
- Create, save, load, and delete playlists
- Import/export playlists as JSON files
- Shuffle and repeat modes
- Drag-and-drop support for adding songs

### 📚 Music Library
- Browse and organize your music collection
- Display song metadata (title, artist, album, duration)
- Add individual files or entire folders
- Double-click to play songs

### 📥 Video-to-MP3 Downloader
- Download videos from various platforms
- Convert to MP3 format automatically
- Multiple quality options (best, 320k, 256k, 192k, 128k)
- Real-time download progress
- Integrated with yt-dlp

### 🎨 Modern Interface
- Clean, intuitive GUI built with tkinter
- Responsive design that works on different screen sizes
- Keyboard shortcuts for common actions
- Professional styling with ttk themes

## Installation

### Prerequisites
- Python 3.7 or higher
- Windows operating system

### Quick Start
1. Clone or download this repository
2. Run the application:
   ```bash
   python app.py
   ```
3. The application will automatically install required dependencies

### Manual Installation
If you prefer to install dependencies manually:
```bash
pip install pygame mutagen yt-dlp Pillow
```

## Usage

### Getting Started
1. Launch the application by running `app.py`
2. Add music files using "Add Files" or "Add Folder" buttons
3. Double-click any song to start playing
4. Use the playback controls to manage playback

### Creating Playlists
1. Go to Playlist menu → New Playlist
2. Enter a name for your playlist
3. Add songs by selecting them in the library
4. Save your playlist for future use

### Downloading Music
1. Go to Tools menu → Video to MP3 Downloader
2. Enter a video URL (YouTube, etc.)
3. Choose output directory and quality
4. Click Download to start the process

### Keyboard Shortcuts
- **Space**: Play/Pause
- **Left Arrow**: Previous song
- **Right Arrow**: Next song

## File Structure

```
videomp3/
├── app.py                 # Main application launcher
├── music_player.py        # Main GUI application
├── audio_player.py        # Audio playback engine
├── playlist_manager.py    # Playlist management
├── downloader_gui.py      # Video downloader interface
├── requirements.txt       # Python dependencies
├── playlists/            # Saved playlists directory
├── yt-dlp.exe           # yt-dlp executable (Windows)
└── README.md            # This file
```

## Dependencies

- **pygame**: Audio playback engine
- **mutagen**: Audio metadata extraction
- **yt-dlp**: Video downloading and conversion
- **Pillow**: Image processing (for future album art support)
- **tkinter**: GUI framework (included with Python)

## Supported Audio Formats

- MP3 (.mp3)
- WAV (.wav)
- OGG (.ogg)
- FLAC (.flac)
- M4A (.m4a)
- WebM (.webm)

## Troubleshooting

### Audio Playback Issues
- Ensure pygame is properly installed
- Check that audio files are not corrupted
- Verify file format is supported
- **WebM files**: Only WebM files with Vorbis audio codec are supported. VP8/VP9 video with Opus audio may not work.

### Download Issues
- Ensure yt-dlp.exe is in the application directory
- Check internet connection
- Verify the video URL is valid and accessible

### Performance Issues
- Close other audio applications
- Ensure sufficient disk space for downloads
- Check system audio settings

## Contributing

Feel free to contribute to this project by:
- Reporting bugs
- Suggesting new features
- Submitting pull requests
- Improving documentation

## License

This project is open source and available under the MIT License.

## Acknowledgments

- Built with Python and tkinter
- Audio playback powered by pygame
- Video downloading powered by yt-dlp
- Metadata extraction using mutagen

---

**Music Player Pro** - Enjoy your music! 🎵
