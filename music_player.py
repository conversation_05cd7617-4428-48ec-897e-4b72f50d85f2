import tkinter as tk
from tkinter import ttk, filedialog, messagebox, simpledialog
import os
import threading
import time
from audio_player import AudioPlayer
from playlist_manager import PlaylistManager
from downloader_gui import DownloaderGUI

class MusicPlayerGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Music Player Pro")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)
        
        # Initialize components
        self.audio_player = AudioPlayer()
        self.playlist_manager = PlaylistManager()
        self.downloader_gui = DownloaderGUI(self.root)
        
        # Variables
        self.current_song_var = tk.StringVar(value="No song loaded")
        self.time_var = tk.StringVar(value="00:00 / 00:00")
        self.volume_var = tk.DoubleVar(value=70)
        self.position_var = tk.DoubleVar(value=0)
        self.is_seeking = False
        self.repeat_mode = False
        self.shuffle_mode = False
        
        # Supported audio formats
        self.supported_formats = ('.mp3', '.wav', '.ogg', '.flac', '.m4a')
        
        self.create_widgets()
        self.setup_bindings()
        self.start_update_thread()
        
        # Load default playlist if exists
        self.load_music_library()
    
    def create_widgets(self):
        """Create all GUI widgets"""
        # Configure style
        style = ttk.Style()
        style.theme_use('clam')
        
        # Main container
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Top frame for current song info
        self.create_song_info_frame(main_frame)
        
        # Middle frame for controls
        self.create_controls_frame(main_frame)
        
        # Bottom frame with playlist and library
        self.create_content_frame(main_frame)
        
        # Menu bar
        self.create_menu_bar()
    
    def create_song_info_frame(self, parent):
        """Create song information display"""
        info_frame = ttk.LabelFrame(parent, text="Now Playing", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Song title
        song_label = ttk.Label(info_frame, textvariable=self.current_song_var, 
                              font=('Arial', 12, 'bold'))
        song_label.pack(anchor=tk.W)
        
        # Time display
        time_label = ttk.Label(info_frame, textvariable=self.time_var)
        time_label.pack(anchor=tk.W)
        
        # Progress bar
        self.progress_scale = ttk.Scale(info_frame, from_=0, to=100, 
                                       variable=self.position_var, orient=tk.HORIZONTAL)
        self.progress_scale.pack(fill=tk.X, pady=(5, 0))
        self.progress_scale.bind("<Button-1>", self.on_seek_start)
        self.progress_scale.bind("<ButtonRelease-1>", self.on_seek_end)
    
    def create_controls_frame(self, parent):
        """Create playback controls"""
        controls_frame = ttk.Frame(parent)
        controls_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Playback buttons
        button_frame = ttk.Frame(controls_frame)
        button_frame.pack(side=tk.LEFT)
        
        self.prev_btn = ttk.Button(button_frame, text="⏮", command=self.previous_song, width=3)
        self.prev_btn.pack(side=tk.LEFT, padx=2)
        
        self.play_btn = ttk.Button(button_frame, text="▶", command=self.toggle_play, width=3)
        self.play_btn.pack(side=tk.LEFT, padx=2)
        
        self.stop_btn = ttk.Button(button_frame, text="⏹", command=self.stop_song, width=3)
        self.stop_btn.pack(side=tk.LEFT, padx=2)
        
        self.next_btn = ttk.Button(button_frame, text="⏭", command=self.next_song, width=3)
        self.next_btn.pack(side=tk.LEFT, padx=2)
        
        # Mode buttons
        mode_frame = ttk.Frame(controls_frame)
        mode_frame.pack(side=tk.LEFT, padx=(20, 0))
        
        self.shuffle_btn = ttk.Button(mode_frame, text="🔀", command=self.toggle_shuffle, width=3)
        self.shuffle_btn.pack(side=tk.LEFT, padx=2)
        
        self.repeat_btn = ttk.Button(mode_frame, text="🔁", command=self.toggle_repeat, width=3)
        self.repeat_btn.pack(side=tk.LEFT, padx=2)
        
        # Volume control
        volume_frame = ttk.Frame(controls_frame)
        volume_frame.pack(side=tk.RIGHT)
        
        ttk.Label(volume_frame, text="🔊").pack(side=tk.LEFT)
        volume_scale = ttk.Scale(volume_frame, from_=0, to=100, variable=self.volume_var,
                               orient=tk.HORIZONTAL, length=100, command=self.on_volume_change)
        volume_scale.pack(side=tk.LEFT, padx=(5, 0))
    
    def create_content_frame(self, parent):
        """Create main content area with playlist and library"""
        content_frame = ttk.Frame(parent)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # Left panel - Playlists
        left_frame = ttk.LabelFrame(content_frame, text="Playlists", padding="5")
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # Playlist controls
        playlist_controls = ttk.Frame(left_frame)
        playlist_controls.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Button(playlist_controls, text="New", command=self.new_playlist).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Button(playlist_controls, text="Load", command=self.load_playlist).pack(side=tk.LEFT, padx=2)
        ttk.Button(playlist_controls, text="Save", command=self.save_playlist).pack(side=tk.LEFT, padx=2)
        ttk.Button(playlist_controls, text="Delete", command=self.delete_playlist).pack(side=tk.LEFT, padx=2)
        
        # Playlist listbox
        playlist_frame = ttk.Frame(left_frame)
        playlist_frame.pack(fill=tk.BOTH, expand=True)
        
        self.playlist_listbox = tk.Listbox(playlist_frame, selectmode=tk.SINGLE)
        playlist_scrollbar = ttk.Scrollbar(playlist_frame, orient=tk.VERTICAL, command=self.playlist_listbox.yview)
        self.playlist_listbox.configure(yscrollcommand=playlist_scrollbar.set)
        
        self.playlist_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        playlist_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Right panel - Music Library
        right_frame = ttk.LabelFrame(content_frame, text="Music Library", padding="5")
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        # Library controls
        library_controls = ttk.Frame(right_frame)
        library_controls.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Button(library_controls, text="Add Files", command=self.add_files).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Button(library_controls, text="Add Folder", command=self.add_folder).pack(side=tk.LEFT, padx=2)
        ttk.Button(library_controls, text="Refresh", command=self.refresh_library).pack(side=tk.LEFT, padx=2)
        
        # Library treeview
        library_frame = ttk.Frame(right_frame)
        library_frame.pack(fill=tk.BOTH, expand=True)
        
        columns = ('Title', 'Artist', 'Album', 'Duration')
        self.library_tree = ttk.Treeview(library_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        self.library_tree.heading('Title', text='Title')
        self.library_tree.heading('Artist', text='Artist')
        self.library_tree.heading('Album', text='Album')
        self.library_tree.heading('Duration', text='Duration')
        
        self.library_tree.column('Title', width=200)
        self.library_tree.column('Artist', width=150)
        self.library_tree.column('Album', width=150)
        self.library_tree.column('Duration', width=80)
        
        library_scrollbar = ttk.Scrollbar(library_frame, orient=tk.VERTICAL, command=self.library_tree.yview)
        self.library_tree.configure(yscrollcommand=library_scrollbar.set)
        
        self.library_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        library_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_menu_bar(self):
        """Create menu bar"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Add Files", command=self.add_files)
        file_menu.add_command(label="Add Folder", command=self.add_folder)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)
        
        # Playlist menu
        playlist_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Playlist", menu=playlist_menu)
        playlist_menu.add_command(label="New Playlist", command=self.new_playlist)
        playlist_menu.add_command(label="Load Playlist", command=self.load_playlist)
        playlist_menu.add_command(label="Save Playlist", command=self.save_playlist)
        playlist_menu.add_separator()
        playlist_menu.add_command(label="Import Playlist", command=self.import_playlist)
        playlist_menu.add_command(label="Export Playlist", command=self.export_playlist)
        
        # Tools menu
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Tools", menu=tools_menu)
        tools_menu.add_command(label="Video to MP3 Downloader", command=self.downloader_gui.show_downloader)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self.show_about)
    
    def setup_bindings(self):
        """Setup event bindings"""
        self.library_tree.bind('<Double-1>', self.on_library_double_click)
        self.playlist_listbox.bind('<Double-1>', self.on_playlist_double_click)
        self.root.bind('<space>', lambda e: self.toggle_play())
        self.root.bind('<Left>', lambda e: self.previous_song())
        self.root.bind('<Right>', lambda e: self.next_song())
    
    def format_time(self, seconds):
        """Format time in MM:SS format"""
        if seconds < 0:
            seconds = 0
        minutes = int(seconds // 60)
        seconds = int(seconds % 60)
        return f"{minutes:02d}:{seconds:02d}"

    def toggle_play(self):
        """Toggle play/pause"""
        if self.audio_player.is_playing:
            self.audio_player.pause()
            self.play_btn.config(text="▶")
        else:
            if self.audio_player.current_song:
                self.audio_player.play()
                self.play_btn.config(text="⏸")
            else:
                # No song loaded, try to play first song in library
                self.play_first_available_song()

    def stop_song(self):
        """Stop current song"""
        self.audio_player.stop()
        self.play_btn.config(text="▶")
        self.position_var.set(0)

    def next_song(self):
        """Play next song"""
        next_song = self.playlist_manager.next_song()
        if next_song and os.path.exists(next_song):
            self.load_and_play_song(next_song)
        elif not self.repeat_mode:
            self.stop_song()

    def previous_song(self):
        """Play previous song"""
        prev_song = self.playlist_manager.previous_song()
        if prev_song and os.path.exists(prev_song):
            self.load_and_play_song(prev_song)

    def toggle_shuffle(self):
        """Toggle shuffle mode"""
        self.shuffle_mode = not self.shuffle_mode
        if self.shuffle_mode:
            self.shuffle_btn.config(relief=tk.SUNKEN)
            self.playlist_manager.shuffle_playlist()
        else:
            self.shuffle_btn.config(relief=tk.RAISED)

    def toggle_repeat(self):
        """Toggle repeat mode"""
        self.repeat_mode = not self.repeat_mode
        if self.repeat_mode:
            self.repeat_btn.config(relief=tk.SUNKEN)
        else:
            self.repeat_btn.config(relief=tk.RAISED)

    def on_volume_change(self, value):
        """Handle volume change"""
        volume = float(value) / 100.0
        self.audio_player.set_volume(volume)

    def on_seek_start(self, event):
        """Handle seek start"""
        self.is_seeking = True

    def on_seek_end(self, event):
        """Handle seek end"""
        if self.audio_player.current_song:
            position = self.position_var.get() / 100.0 * self.audio_player.get_duration()
            self.audio_player.set_position(position)
        self.is_seeking = False

    def load_and_play_song(self, file_path):
        """Load and play a song"""
        if self.audio_player.load_song(file_path):
            metadata = self.audio_player.get_song_metadata(file_path)
            self.current_song_var.set(f"{metadata['artist']} - {metadata['title']}")
            self.audio_player.play()
            self.play_btn.config(text="⏸")
        else:
            messagebox.showerror("Error", f"Could not load song: {os.path.basename(file_path)}")

    def play_first_available_song(self):
        """Play the first available song in the library"""
        for item in self.library_tree.get_children():
            values = self.library_tree.item(item)['values']
            if len(values) >= 4:
                # Get file path from item data
                file_path = self.library_tree.set(item, 'file_path')
                if file_path and os.path.exists(file_path):
                    self.load_and_play_song(file_path)
                    # Set up playlist with all library songs
                    all_songs = []
                    for lib_item in self.library_tree.get_children():
                        lib_path = self.library_tree.set(lib_item, 'file_path')
                        if lib_path and os.path.exists(lib_path):
                            all_songs.append(lib_path)
                    self.playlist_manager.set_current_playlist(all_songs, 0)
                    break

    def add_files(self):
        """Add music files to library"""
        files = filedialog.askopenfilenames(
            title="Select Music Files",
            filetypes=[
                ("Audio Files", "*.mp3 *.wav *.ogg *.flac *.m4a"),
                ("MP3 Files", "*.mp3"),
                ("WAV Files", "*.wav"),
                ("All Files", "*.*")
            ]
        )

        for file_path in files:
            self.add_song_to_library(file_path)

    def add_folder(self):
        """Add all music files from a folder"""
        folder = filedialog.askdirectory(title="Select Music Folder")
        if folder:
            for root, dirs, files in os.walk(folder):
                for file in files:
                    if file.lower().endswith(self.supported_formats):
                        file_path = os.path.join(root, file)
                        self.add_song_to_library(file_path)

    def add_song_to_library(self, file_path):
        """Add a single song to the library"""
        if not os.path.exists(file_path):
            return

        # Check if song already exists
        for item in self.library_tree.get_children():
            if self.library_tree.set(item, 'file_path') == file_path:
                return  # Song already in library

        metadata = self.audio_player.get_song_metadata(file_path)
        duration_str = self.format_time(metadata['duration'])

        item = self.library_tree.insert('', tk.END, values=(
            metadata['title'],
            metadata['artist'],
            metadata['album'],
            duration_str
        ))

        # Store file path as item data
        self.library_tree.set(item, 'file_path', file_path)

    def refresh_library(self):
        """Refresh the music library"""
        # Clear current library
        for item in self.library_tree.get_children():
            self.library_tree.delete(item)

        # Reload library (you might want to save/load library state)
        self.load_music_library()

    def load_music_library(self):
        """Load music library from saved state or scan default directories"""
        # For now, just scan current directory for music files
        for file in os.listdir('.'):
            if file.lower().endswith(self.supported_formats):
                self.add_song_to_library(file)

    def on_library_double_click(self, event):
        """Handle double-click on library item"""
        selection = self.library_tree.selection()
        if selection:
            item = selection[0]
            file_path = self.library_tree.set(item, 'file_path')
            if file_path and os.path.exists(file_path):
                self.load_and_play_song(file_path)

                # Set up playlist with all library songs
                all_songs = []
                current_index = 0
                for i, lib_item in enumerate(self.library_tree.get_children()):
                    lib_path = self.library_tree.set(lib_item, 'file_path')
                    if lib_path and os.path.exists(lib_path):
                        all_songs.append(lib_path)
                        if lib_path == file_path:
                            current_index = i

                self.playlist_manager.set_current_playlist(all_songs, current_index)

    def on_playlist_double_click(self, event):
        """Handle double-click on playlist item"""
        selection = self.playlist_listbox.curselection()
        if selection:
            index = selection[0]
            playlist_name = self.playlist_listbox.get(index)
            playlist = self.playlist_manager.get_playlist(playlist_name)
            if playlist and playlist['songs']:
                # Load first song from playlist
                first_song = playlist['songs'][0]
                if os.path.exists(first_song):
                    self.load_and_play_song(first_song)
                    self.playlist_manager.set_current_playlist(playlist['songs'], 0)

    def new_playlist(self):
        """Create a new playlist"""
        name = simpledialog.askstring("New Playlist", "Enter playlist name:")
        if name:
            if self.playlist_manager.create_playlist(name):
                self.refresh_playlists()
                messagebox.showinfo("Success", f"Playlist '{name}' created successfully!")
            else:
                messagebox.showerror("Error", "Failed to create playlist")

    def load_playlist(self):
        """Load an existing playlist"""
        playlists = self.playlist_manager.get_playlist_names()
        if not playlists:
            messagebox.showinfo("Info", "No playlists available")
            return

        # Create selection dialog
        dialog = tk.Toplevel(self.root)
        dialog.title("Load Playlist")
        dialog.geometry("300x200")
        dialog.transient(self.root)
        dialog.grab_set()

        ttk.Label(dialog, text="Select a playlist:").pack(pady=10)

        listbox = tk.Listbox(dialog)
        listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

        for playlist in playlists:
            listbox.insert(tk.END, playlist)

        def on_select():
            selection = listbox.curselection()
            if selection:
                playlist_name = listbox.get(selection[0])
                playlist = self.playlist_manager.get_playlist(playlist_name)
                if playlist and playlist['songs']:
                    self.playlist_manager.set_current_playlist(playlist['songs'], 0)
                    messagebox.showinfo("Success", f"Loaded playlist '{playlist_name}'")
                dialog.destroy()

        ttk.Button(dialog, text="Load", command=on_select).pack(pady=5)
        ttk.Button(dialog, text="Cancel", command=dialog.destroy).pack()

    def save_playlist(self):
        """Save current playlist"""
        if not self.playlist_manager.current_playlist:
            messagebox.showinfo("Info", "No current playlist to save")
            return

        name = simpledialog.askstring("Save Playlist", "Enter playlist name:")
        if name:
            if self.playlist_manager.create_playlist(name, self.playlist_manager.current_playlist):
                self.refresh_playlists()
                messagebox.showinfo("Success", f"Playlist '{name}' saved successfully!")
            else:
                messagebox.showerror("Error", "Failed to save playlist")

    def delete_playlist(self):
        """Delete a playlist"""
        playlists = self.playlist_manager.get_playlist_names()
        if not playlists:
            messagebox.showinfo("Info", "No playlists to delete")
            return

        # Create selection dialog
        dialog = tk.Toplevel(self.root)
        dialog.title("Delete Playlist")
        dialog.geometry("300x200")
        dialog.transient(self.root)
        dialog.grab_set()

        ttk.Label(dialog, text="Select a playlist to delete:").pack(pady=10)

        listbox = tk.Listbox(dialog)
        listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

        for playlist in playlists:
            listbox.insert(tk.END, playlist)

        def on_delete():
            selection = listbox.curselection()
            if selection:
                playlist_name = listbox.get(selection[0])
                if messagebox.askyesno("Confirm", f"Delete playlist '{playlist_name}'?"):
                    if self.playlist_manager.delete_playlist(playlist_name):
                        self.refresh_playlists()
                        messagebox.showinfo("Success", f"Playlist '{playlist_name}' deleted")
                        dialog.destroy()
                    else:
                        messagebox.showerror("Error", "Failed to delete playlist")

        ttk.Button(dialog, text="Delete", command=on_delete).pack(pady=5)
        ttk.Button(dialog, text="Cancel", command=dialog.destroy).pack()

    def refresh_playlists(self):
        """Refresh the playlists listbox"""
        self.playlist_listbox.delete(0, tk.END)
        for playlist_name in self.playlist_manager.get_playlist_names():
            self.playlist_listbox.insert(tk.END, playlist_name)

    def import_playlist(self):
        """Import a playlist from file"""
        file_path = filedialog.askopenfilename(
            title="Import Playlist",
            filetypes=[("JSON Files", "*.json"), ("All Files", "*.*")]
        )

        if file_path:
            playlist_name = self.playlist_manager.import_playlist(file_path)
            if playlist_name:
                self.refresh_playlists()
                messagebox.showinfo("Success", f"Imported playlist '{playlist_name}'")
            else:
                messagebox.showerror("Error", "Failed to import playlist")

    def export_playlist(self):
        """Export a playlist to file"""
        playlists = self.playlist_manager.get_playlist_names()
        if not playlists:
            messagebox.showinfo("Info", "No playlists to export")
            return

        # Create selection dialog
        dialog = tk.Toplevel(self.root)
        dialog.title("Export Playlist")
        dialog.geometry("300x200")
        dialog.transient(self.root)
        dialog.grab_set()

        ttk.Label(dialog, text="Select a playlist to export:").pack(pady=10)

        listbox = tk.Listbox(dialog)
        listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

        for playlist in playlists:
            listbox.insert(tk.END, playlist)

        def on_export():
            selection = listbox.curselection()
            if selection:
                playlist_name = listbox.get(selection[0])
                export_path = filedialog.asksaveasfilename(
                    title="Export Playlist",
                    defaultextension=".json",
                    filetypes=[("JSON Files", "*.json"), ("All Files", "*.*")],
                    initialvalue=f"{playlist_name}.json"
                )

                if export_path:
                    if self.playlist_manager.export_playlist(playlist_name, export_path):
                        messagebox.showinfo("Success", f"Exported playlist to {export_path}")
                        dialog.destroy()
                    else:
                        messagebox.showerror("Error", "Failed to export playlist")

        ttk.Button(dialog, text="Export", command=on_export).pack(pady=5)
        ttk.Button(dialog, text="Cancel", command=dialog.destroy).pack()

    def show_about(self):
        """Show about dialog"""
        about_text = """Music Player Pro v1.0

A modern music player with playlist management and video-to-MP3 downloading capabilities.

Features:
• Play multiple audio formats (MP3, WAV, OGG, FLAC, M4A)
• Create and manage playlists
• Download videos and convert to MP3
• Shuffle and repeat modes
• Volume control and seeking
• Music library management

Developed with Python and tkinter."""

        messagebox.showinfo("About Music Player Pro", about_text)

    def start_update_thread(self):
        """Start the GUI update thread"""
        def update_gui():
            while True:
                try:
                    if not self.is_seeking and self.audio_player.current_song:
                        # Update position
                        position = self.audio_player.get_position()
                        duration = self.audio_player.get_duration()

                        if duration > 0:
                            progress = (position / duration) * 100
                            self.position_var.set(progress)

                        # Update time display
                        time_str = f"{self.format_time(position)} / {self.format_time(duration)}"
                        self.time_var.set(time_str)

                        # Check if song ended
                        if not self.audio_player.is_song_playing() and self.audio_player.is_playing:
                            self.audio_player.is_playing = False
                            self.play_btn.config(text="▶")

                            # Auto-play next song
                            if self.repeat_mode:
                                self.audio_player.play()
                                self.play_btn.config(text="⏸")
                            else:
                                self.next_song()

                    time.sleep(0.1)
                except:
                    break

        update_thread = threading.Thread(target=update_gui)
        update_thread.daemon = True
        update_thread.start()

    def run(self):
        """Start the application"""
        # Load existing playlists
        self.refresh_playlists()

        # Start the main loop
        self.root.mainloop()



def main():
    """Main function to run the music player"""
    try:
        app = MusicPlayerGUI()
        app.run()
    except Exception as e:
        print(f"Error starting music player: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
