#!/usr/bin/env python3
"""
Test WebM file playback specifically
"""

import os
import time
from audio_player import AudioPlayer

def test_webm_playback():
    """Test WebM file playback"""
    print("=" * 60)
    print("🎵 Testing WebM File Playback")
    print("=" * 60)
    
    # Create audio player
    print("\n1. Creating AudioPlayer...")
    player = AudioPlayer()
    
    # Look for WebM files
    webm_files = []
    music_dir = "music"
    
    if os.path.exists(music_dir):
        for file in os.listdir(music_dir):
            if file.lower().endswith('.webm'):
                webm_files.append(os.path.join(music_dir, file))
    
    if not webm_files:
        print("❌ No WebM files found in music directory")
        return False
    
    print(f"📁 Found {len(webm_files)} WebM file(s):")
    for i, file in enumerate(webm_files):
        print(f"   {i+1}. {os.path.basename(file)}")
    
    # Test first WebM file
    test_file = webm_files[0]
    print(f"\n2. Testing WebM file: {os.path.basename(test_file)}")
    
    # Try to load the file
    print("   Attempting to load WebM file...")
    if player.load_song(test_file):
        print("   ✅ WebM file loaded successfully!")
        
        # Try to play
        print("   Attempting to play WebM file...")
        player.play()
        
        # Wait and check if it's playing
        time.sleep(2)
        
        if player.is_song_playing():
            print("   ✅ WebM file is playing!")
            print("   🎵 Playing for 5 seconds...")
            time.sleep(5)
            
            # Stop playback
            player.stop()
            print("   ⏹️  Stopped playback")
            return True
        else:
            print("   ❌ WebM file is not playing")
            print("   💡 This WebM file may use an unsupported audio codec")
            return False
    else:
        print("   ❌ Failed to load WebM file")
        print("   💡 WebM files with Opus audio may not be supported")
        print("   💡 Try converting to MP3 using the downloader")
        return False

def main():
    """Main test function"""
    try:
        print("🔧 This test will check if WebM files can be played")
        print("📝 Note: WebM support depends on the audio codec used")
        
        success = test_webm_playback()
        
        print("\n" + "=" * 60)
        if success:
            print("✅ WebM playback test PASSED!")
            print("Your WebM files should work in the music player.")
        else:
            print("❌ WebM playback test FAILED!")
            print("Your WebM files may use unsupported audio codecs.")
            print("\n💡 Solutions:")
            print("1. Use the Video-to-MP3 downloader to convert WebM to MP3")
            print("2. Try different WebM files with Vorbis audio codec")
            print("3. Use MP3 or WAV files instead")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
