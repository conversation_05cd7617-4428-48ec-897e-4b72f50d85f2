#!/usr/bin/env python3
"""
Music Player Launcher - Tries Python version first, falls back to HTML
"""

import subprocess
import sys
import os
import webbrowser
import time

def test_audio_system():
    """Test if the audio system is working"""
    try:
        import pygame
        pygame.mixer.init()
        
        # Try to create a simple test sound
        import numpy as np
        duration = 0.1
        sample_rate = 22050
        frames = int(duration * sample_rate)
        arr = np.zeros((frames, 2))
        
        for i in range(frames):
            wave = np.sin(2 * np.pi * 440 * i / sample_rate)
            arr[i][0] = wave * 0.1
            arr[i][1] = wave * 0.1
        
        sound_array = (arr * 32767).astype(np.int16)
        sound = pygame.sndarray.make_sound(sound_array)
        sound.play()
        time.sleep(0.2)
        
        pygame.mixer.quit()
        return True
        
    except ImportError:
        print("⚠️  NumPy not available for audio test")
        try:
            import pygame
            pygame.mixer.init()
            pygame.mixer.quit()
            return True
        except:
            return False
    except Exception as e:
        print(f"⚠️  Audio test failed: {e}")
        return False

def launch_python_player():
    """Launch the Python music player"""
    try:
        print("🎵 Launching Python Music Player...")
        
        # Test if required modules are available
        import pygame
        import tkinter
        
        # Test audio system
        if not test_audio_system():
            print("⚠️  Audio system test failed")
            return False
        
        # Launch the simple music player
        from simple_music_player import SimpleMusicPlayer
        app = SimpleMusicPlayer()
        app.run()
        return True
        
    except ImportError as e:
        print(f"❌ Missing required module: {e}")
        return False
    except Exception as e:
        print(f"❌ Error launching Python player: {e}")
        return False

def launch_html_player():
    """Launch the HTML music player"""
    try:
        print("🌐 Launching HTML Music Player...")
        
        # Get the full path to the HTML file
        html_file = os.path.abspath("music_player.html")
        
        if not os.path.exists(html_file):
            print("❌ HTML music player file not found")
            return False
        
        # Open in default browser
        webbrowser.open(f"file://{html_file}")
        print("✅ HTML Music Player opened in browser")
        return True
        
    except Exception as e:
        print(f"❌ Error launching HTML player: {e}")
        return False

def main():
    """Main launcher function"""
    print("=" * 60)
    print("🎵 Music Player Pro - Universal Launcher")
    print("=" * 60)
    
    print("\nThis launcher will try to run the best available music player:")
    print("1. Python version (with pygame audio)")
    print("2. HTML version (web browser fallback)")
    
    # Ask user preference
    print("\nOptions:")
    print("1. Try Python version first (recommended)")
    print("2. Launch HTML version directly")
    print("3. Auto-detect best option")
    
    choice = input("\nEnter your choice (1-3, or press Enter for auto): ").strip()
    
    if choice == "2":
        # Launch HTML directly
        if launch_html_player():
            print("\n✅ HTML Music Player launched successfully!")
            print("🌐 The music player is now running in your web browser.")
            print("📁 You can drag and drop audio files or use the 'Select Audio Files' button.")
        else:
            print("\n❌ Failed to launch HTML music player.")
        return
    
    elif choice == "1" or choice == "" or choice == "3":
        # Try Python first, fallback to HTML
        print("\n🔍 Testing Python music player...")
        
        if launch_python_player():
            print("\n✅ Python Music Player launched successfully!")
        else:
            print("\n⚠️  Python music player failed. Trying HTML fallback...")
            
            if launch_html_player():
                print("\n✅ HTML Music Player launched as fallback!")
                print("🌐 The music player is now running in your web browser.")
                print("📁 You can drag and drop audio files or use the 'Select Audio Files' button.")
                print("\n💡 Note: The HTML version supports all audio formats that your browser supports.")
            else:
                print("\n❌ Both Python and HTML music players failed.")
                print("\n🔧 Troubleshooting:")
                print("- Install required Python packages: pip install pygame tkinter")
                print("- Ensure you have a web browser installed")
                print("- Check that audio files are accessible")
    
    else:
        print("❌ Invalid choice. Please run the launcher again.")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
