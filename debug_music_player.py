#!/usr/bin/env python3
"""
Debug version of the music player to identify issues
"""

import sys
import traceback

def debug_music_player():
    """Debug the music player step by step"""
    try:
        print("Step 1: Testing imports...")
        
        # Test individual imports
        print("  - Importing tkinter...")
        import tkinter as tk
        from tkinter import ttk, filedialog, messagebox, simpledialog
        print("  ✓ tkinter imported successfully")
        
        print("  - Importing other modules...")
        import os
        import threading
        import time
        print("  ✓ Standard modules imported successfully")
        
        print("  - Importing pygame...")
        import pygame
        print("  ✓ pygame imported successfully")
        
        print("  - Importing mutagen...")
        from mutagen import File
        print("  ✓ mutagen imported successfully")
        
        print("Step 2: Testing custom modules...")
        print("  - Importing audio_player...")
        from audio_player import AudioPlayer
        print("  ✓ audio_player imported successfully")
        
        print("  - Importing playlist_manager...")
        from playlist_manager import PlaylistManager
        print("  ✓ playlist_manager imported successfully")
        
        print("  - Importing downloader_gui...")
        from downloader_gui import DownloaderGUI
        print("  ✓ downloader_gui imported successfully")
        
        print("Step 3: Testing component initialization...")
        print("  - Creating AudioPlayer...")
        audio_player = AudioPlayer()
        print("  ✓ AudioPlayer created successfully")
        
        print("  - Creating PlaylistManager...")
        playlist_manager = PlaylistManager()
        print("  ✓ PlaylistManager created successfully")
        
        print("Step 4: Testing GUI creation...")
        print("  - Creating root window...")
        root = tk.Tk()
        root.title("Music Player Pro - Debug")
        root.geometry("800x600")
        print("  ✓ Root window created successfully")
        
        print("  - Creating DownloaderGUI...")
        downloader_gui = DownloaderGUI(root)
        print("  ✓ DownloaderGUI created successfully")
        
        print("Step 5: Testing basic GUI elements...")
        frame = ttk.Frame(root)
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        label = ttk.Label(frame, text="Music Player Pro - Debug Mode")
        label.pack(pady=10)
        
        def close_debug():
            print("Closing debug window...")
            root.destroy()
        
        close_btn = ttk.Button(frame, text="Close Debug", command=close_debug)
        close_btn.pack(pady=10)
        
        print("Step 6: Starting GUI mainloop...")
        print("✅ All components initialized successfully!")
        print("🖥️  Debug window should be visible now...")
        
        # Start the GUI
        root.mainloop()
        
        print("✅ Debug completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error during debug: {e}")
        print("📋 Full traceback:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🔧 Music Player Pro - Debug Mode")
    print("=" * 60)
    
    if debug_music_player():
        print("\n✅ Debug completed successfully!")
        print("The music player components are working correctly.")
    else:
        print("\n❌ Debug failed!")
        print("There's an issue with the music player components.")
