<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Music Player Pro - Web Version</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .player-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            width: 100%;
            backdrop-filter: blur(10px);
        }

        .player-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .player-header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .player-header p {
            color: #666;
            font-size: 1.1em;
        }

        .now-playing {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 25px;
            text-align: center;
        }

        .song-title {
            font-size: 1.4em;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .song-info {
            color: #666;
            margin-bottom: 15px;
        }

        .progress-container {
            margin: 20px 0;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            cursor: pointer;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.1s ease;
        }

        .time-display {
            display: flex;
            justify-content: space-between;
            margin-top: 8px;
            font-size: 0.9em;
            color: #666;
        }

        .controls {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 15px;
            margin: 25px 0;
        }

        .control-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            color: white;
            font-size: 1.2em;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .control-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .control-btn.play {
            width: 60px;
            height: 60px;
            font-size: 1.5em;
        }

        .volume-container {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 20px 0;
        }

        .volume-slider {
            flex: 1;
            height: 6px;
            background: #e0e0e0;
            border-radius: 3px;
            outline: none;
            cursor: pointer;
        }

        .file-input-container {
            margin: 25px 0;
            text-align: center;
        }

        .file-input {
            display: none;
        }

        .file-label {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            display: inline-block;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .file-label:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .playlist {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            max-height: 200px;
            overflow-y: auto;
        }

        .playlist-title {
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }

        .playlist-item {
            padding: 10px;
            border-radius: 8px;
            cursor: pointer;
            transition: background 0.2s ease;
            margin-bottom: 5px;
        }

        .playlist-item:hover {
            background: #e9ecef;
        }

        .playlist-item.active {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .playlist-empty {
            text-align: center;
            color: #999;
            font-style: italic;
            padding: 20px;
        }

        @media (max-width: 480px) {
            .player-container {
                padding: 20px;
            }
            
            .player-header h1 {
                font-size: 2em;
            }
            
            .controls {
                gap: 10px;
            }
            
            .control-btn {
                width: 45px;
                height: 45px;
            }
            
            .control-btn.play {
                width: 55px;
                height: 55px;
            }
        }
    </style>
</head>
<body>
    <div class="player-container">
        <div class="player-header">
            <h1>🎵 Music Player Pro</h1>
            <p>Web Version - Universal Audio Support</p>
        </div>

        <div class="now-playing">
            <div class="song-title" id="songTitle">No song loaded</div>
            <div class="song-info" id="songInfo">Select an audio file to start playing</div>
            
            <div class="progress-container">
                <div class="progress-bar" id="progressBar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="time-display">
                    <span id="currentTime">00:00</span>
                    <span id="totalTime">00:00</span>
                </div>
            </div>
        </div>

        <div class="controls">
            <button class="control-btn" id="prevBtn" title="Previous">⏮</button>
            <button class="control-btn play" id="playBtn" title="Play/Pause">▶</button>
            <button class="control-btn" id="stopBtn" title="Stop">⏹</button>
            <button class="control-btn" id="nextBtn" title="Next">⏭</button>
        </div>

        <div class="volume-container">
            <span>🔊</span>
            <input type="range" class="volume-slider" id="volumeSlider" min="0" max="100" value="70">
            <span id="volumeDisplay">70%</span>
        </div>

        <div class="file-input-container">
            <input type="file" class="file-input" id="fileInput" multiple accept="audio/*">
            <label for="fileInput" class="file-label">📁 Select Audio Files</label>
        </div>

        <div class="download-section" style="background: #f8f9fa; border-radius: 15px; padding: 20px; margin: 20px 0;">
            <h3 style="margin-bottom: 15px; color: #333;">📥 Download Audio from URL</h3>

            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">Video/Audio URL:</label>
                <input type="url" id="urlInput" placeholder="https://www.youtube.com/watch?v=..." style="width: 100%; padding: 12px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 16px;" />
            </div>

            <div style="display: flex; gap: 10px; margin-bottom: 15px;">
                <div style="flex: 1;">
                    <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">Format:</label>
                    <select id="formatSelect" style="width: 100%; padding: 12px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 16px;">
                        <option value="mp3">MP3 (Recommended)</option>
                        <option value="wav">WAV (High Quality)</option>
                        <option value="ogg">OGG (Open Source)</option>
                        <option value="m4a">M4A (Apple)</option>
                    </select>
                </div>
                <div style="flex: 1;">
                    <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">Quality:</label>
                    <select id="qualitySelect" style="width: 100%; padding: 12px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 16px;">
                        <option value="best">Best Available</option>
                        <option value="320">320 kbps</option>
                        <option value="256">256 kbps</option>
                        <option value="192">192 kbps</option>
                        <option value="128">128 kbps</option>
                    </select>
                </div>
            </div>

            <button id="downloadBtn" onclick="downloadAudio()" style="background: linear-gradient(45deg, #28a745, #20c997); color: white; border: none; padding: 15px 30px; border-radius: 25px; font-size: 16px; font-weight: 500; cursor: pointer; width: 100%; transition: all 0.3s ease;">
                📥 Download Audio
            </button>

            <div id="statusMessage" style="margin-top: 15px; padding: 10px; border-radius: 8px; text-align: center; font-weight: 500; display: none;"></div>
        </div>

        <div class="playlist">
            <div class="playlist-title">
                Playlist
                <button class="clear-btn" onclick="clearPlaylist()" style="background: #dc3545; color: white; border: none; border-radius: 15px; padding: 5px 12px; cursor: pointer; font-size: 12px;">Clear All</button>
            </div>
            <div id="playlistContainer">
                <div class="playlist-empty">No songs in playlist. Select audio files to get started!</div>
            </div>
        </div>

        <audio id="audioPlayer" preload="metadata"></audio>
    </div>

    <script>
        class MusicPlayer {
            constructor() {
                this.audio = document.getElementById('audioPlayer');
                this.playlist = [];
                this.currentIndex = 0;
                this.isPlaying = false;
                
                this.initializeElements();
                this.bindEvents();
                this.updateDisplay();
            }

            initializeElements() {
                this.songTitle = document.getElementById('songTitle');
                this.songInfo = document.getElementById('songInfo');
                this.playBtn = document.getElementById('playBtn');
                this.prevBtn = document.getElementById('prevBtn');
                this.nextBtn = document.getElementById('nextBtn');
                this.stopBtn = document.getElementById('stopBtn');
                this.progressBar = document.getElementById('progressBar');
                this.progressFill = document.getElementById('progressFill');
                this.currentTime = document.getElementById('currentTime');
                this.totalTime = document.getElementById('totalTime');
                this.volumeSlider = document.getElementById('volumeSlider');
                this.volumeDisplay = document.getElementById('volumeDisplay');
                this.fileInput = document.getElementById('fileInput');
                this.playlistContainer = document.getElementById('playlistContainer');
            }

            bindEvents() {
                // Audio events
                this.audio.addEventListener('loadedmetadata', () => this.updateDisplay());
                this.audio.addEventListener('timeupdate', () => this.updateProgress());
                this.audio.addEventListener('ended', () => this.nextSong());
                this.audio.addEventListener('play', () => this.onPlay());
                this.audio.addEventListener('pause', () => this.onPause());

                // Control events
                this.playBtn.addEventListener('click', () => this.togglePlay());
                this.prevBtn.addEventListener('click', () => this.previousSong());
                this.nextBtn.addEventListener('click', () => this.nextSong());
                this.stopBtn.addEventListener('click', () => this.stop());

                // Progress bar
                this.progressBar.addEventListener('click', (e) => this.seek(e));

                // Volume
                this.volumeSlider.addEventListener('input', (e) => this.setVolume(e.target.value));

                // File input
                this.fileInput.addEventListener('change', (e) => this.loadFiles(e.target.files));

                // Keyboard shortcuts
                document.addEventListener('keydown', (e) => {
                    if (e.code === 'Space') {
                        e.preventDefault();
                        this.togglePlay();
                    } else if (e.code === 'ArrowLeft') {
                        this.previousSong();
                    } else if (e.code === 'ArrowRight') {
                        this.nextSong();
                    }
                });
            }

            loadFiles(files) {
                this.playlist = Array.from(files);
                this.currentIndex = 0;
                this.updatePlaylist();
                if (this.playlist.length > 0) {
                    this.loadCurrentSong();
                }
            }

            loadCurrentSong() {
                if (this.playlist.length === 0) return;
                
                const file = this.playlist[this.currentIndex];
                const url = URL.createObjectURL(file);
                this.audio.src = url;
                
                this.songTitle.textContent = file.name;
                this.songInfo.textContent = `Track ${this.currentIndex + 1} of ${this.playlist.length}`;
                
                this.updatePlaylist();
            }

            togglePlay() {
                if (this.playlist.length === 0) {
                    alert('Please select audio files first!');
                    return;
                }

                if (this.isPlaying) {
                    this.audio.pause();
                } else {
                    this.audio.play().catch(e => {
                        console.error('Playback failed:', e);
                        alert('Failed to play audio. Please try a different file.');
                    });
                }
            }

            stop() {
                this.audio.pause();
                this.audio.currentTime = 0;
                this.onPause();
            }

            nextSong() {
                if (this.playlist.length === 0) return;
                
                this.currentIndex = (this.currentIndex + 1) % this.playlist.length;
                this.loadCurrentSong();
                if (this.isPlaying) {
                    this.audio.play();
                }
            }

            previousSong() {
                if (this.playlist.length === 0) return;
                
                this.currentIndex = (this.currentIndex - 1 + this.playlist.length) % this.playlist.length;
                this.loadCurrentSong();
                if (this.isPlaying) {
                    this.audio.play();
                }
            }

            seek(e) {
                if (this.audio.duration) {
                    const rect = this.progressBar.getBoundingClientRect();
                    const percent = (e.clientX - rect.left) / rect.width;
                    this.audio.currentTime = percent * this.audio.duration;
                }
            }

            setVolume(value) {
                this.audio.volume = value / 100;
                this.volumeDisplay.textContent = value + '%';
            }

            onPlay() {
                this.isPlaying = true;
                this.playBtn.textContent = '⏸';
                this.playBtn.title = 'Pause';
            }

            onPause() {
                this.isPlaying = false;
                this.playBtn.textContent = '▶';
                this.playBtn.title = 'Play';
            }

            updateProgress() {
                if (this.audio.duration) {
                    const percent = (this.audio.currentTime / this.audio.duration) * 100;
                    this.progressFill.style.width = percent + '%';
                    
                    this.currentTime.textContent = this.formatTime(this.audio.currentTime);
                    this.totalTime.textContent = this.formatTime(this.audio.duration);
                }
            }

            updateDisplay() {
                if (this.audio.duration) {
                    this.totalTime.textContent = this.formatTime(this.audio.duration);
                }
            }

            updatePlaylist() {
                if (this.playlist.length === 0) {
                    this.playlistContainer.innerHTML = '<div class="playlist-empty">No songs in playlist. Select audio files to get started!</div>';
                    return;
                }

                this.playlistContainer.innerHTML = '';
                this.playlist.forEach((file, index) => {
                    const item = document.createElement('div');
                    item.className = 'playlist-item' + (index === this.currentIndex ? ' active' : '');
                    item.textContent = file.name;
                    item.addEventListener('click', () => {
                        this.currentIndex = index;
                        this.loadCurrentSong();
                        if (this.isPlaying) {
                            this.audio.play();
                        }
                    });
                    this.playlistContainer.appendChild(item);
                });
            }

            formatTime(seconds) {
                if (isNaN(seconds)) return '00:00';
                const mins = Math.floor(seconds / 60);
                const secs = Math.floor(seconds % 60);
                return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            }
        }

        // Clear playlist function
        function clearPlaylist() {
            if (confirm('Clear all songs from playlist?')) {
                musicPlayer.playlist = [];
                musicPlayer.currentIndex = 0;
                musicPlayer.updatePlaylist();
                musicPlayer.songTitle.textContent = 'No song loaded';
                musicPlayer.songInfo.textContent = 'Select an audio file to start playing';
                musicPlayer.audio.src = '';
                musicPlayer.onPause();
            }
        }

        // Download functionality
        async function downloadAudio() {
            const url = document.getElementById('urlInput').value.trim();
            const format = document.getElementById('formatSelect').value;
            const quality = document.getElementById('qualitySelect').value;
            const downloadBtn = document.getElementById('downloadBtn');
            const statusDiv = document.getElementById('statusMessage');

            if (!url) {
                showStatus('Please enter a valid URL', 'error');
                return;
            }

            // Disable button and show loading
            downloadBtn.disabled = true;
            downloadBtn.textContent = '⏳ Processing...';
            showStatus('Preparing download...', 'info');

            try {
                // Try to connect to the Python backend first
                const backendUrl = 'http://localhost:8080';

                const response = await fetch(`${backendUrl}/download`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        url: url,
                        format: format,
                        quality: quality
                    })
                });

                if (response.ok) {
                    const result = await response.json();

                    if (result.success) {
                        showStatus(result.message, 'success');

                        // Try to load the downloaded file into the playlist
                        try {
                            const fileResponse = await fetch(`${backendUrl}/downloads/${result.filename}`);
                            if (fileResponse.ok) {
                                const audioBlob = await fileResponse.blob();
                                const file = new File([audioBlob], result.filename, { type: 'audio/mpeg' });
                                musicPlayer.playlist.push(file);
                                musicPlayer.updatePlaylist();

                                if (musicPlayer.playlist.length === 1) {
                                    musicPlayer.currentIndex = 0;
                                    musicPlayer.loadCurrentSong();
                                }
                            }
                        } catch (e) {
                            showStatus('Downloaded successfully! Check the downloads folder.', 'success');
                        }
                    } else {
                        showStatus(result.error || 'Download failed', 'error');
                    }
                } else {
                    throw new Error('Backend service not available');
                }

            } catch (error) {
                // Fallback to simulation if backend is not available
                console.log('Backend not available, showing demo mode');

                showStatus('Backend service not running - Demo mode', 'info');
                await sleep(1000);

                showStatus('Analyzing URL...', 'info');
                await sleep(1000);

                showStatus('Extracting audio...', 'info');
                await sleep(2000);

                showStatus('Converting to ' + format.toUpperCase() + '...', 'info');
                await sleep(1500);

                showStatus('Demo completed! To enable real downloads:', 'info');
                await sleep(2000);

                showStatus('Run: python download_service.py', 'info');
            } finally {
                downloadBtn.disabled = false;
                downloadBtn.textContent = '📥 Download Audio';
            }
        }

        function showStatus(message, type) {
            const statusDiv = document.getElementById('statusMessage');
            statusDiv.className = '';
            statusDiv.style.display = 'block';

            // Set colors based on type
            if (type === 'success') {
                statusDiv.style.background = '#d4edda';
                statusDiv.style.color = '#155724';
                statusDiv.style.border = '1px solid #c3e6cb';
            } else if (type === 'error') {
                statusDiv.style.background = '#f8d7da';
                statusDiv.style.color = '#721c24';
                statusDiv.style.border = '1px solid #f5c6cb';
            } else {
                statusDiv.style.background = '#d1ecf1';
                statusDiv.style.color = '#0c5460';
                statusDiv.style.border = '1px solid #bee5eb';
            }

            statusDiv.textContent = message;

            // Auto-hide after 5 seconds for non-error messages
            if (type !== 'error') {
                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 5000);
            }
        }

        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // Initialize the music player when the page loads
        let musicPlayer;
        document.addEventListener('DOMContentLoaded', () => {
            musicPlayer = new MusicPlayer();
        });
    </script>
</body>
</html>
