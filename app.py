import subprocess
import sys
import os

def install_package(package):
    """Installs a Python package if it's not already installed."""
    try:
        __import__(package)
        print(f"'{package}' is already installed.")
    except ImportError:
        print(f"'{package}' not found. Installing now...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"'{package}' installed successfully.")
        except subprocess.CalledProcessError as e:
            print(f"Error installing '{package}': {e}")
            sys.exit(1)

def download_and_convert_to_mp3(url, output_path="."):
    """
    Downloads a video from the given URL and converts it to MP3.
    Requires yt-dlp to be installed.
    """
    print(f"Attempting to download and convert: {url}")
    # Ensure output directory exists
    os.makedirs(output_path, exist_ok=True)
    
    command = [
        "yt-dlp",
        "-x",  # Extract audio
        "--audio-format", "mp3",
        "--audio-quality", "0",  # Best audio quality
        "--postprocessor-args", "-codec:a libmp3lame", # Explicitly set MP3 codec for compatibility
        "-o", os.path.join(output_path, "%(title)s.%(ext)s"), # Output filename template
        url
    ]
    try:
        subprocess.run(command, check=True, text=True, capture_output=True)
        print(f"Successfully downloaded and converted {url} to MP3.")
        print(f"Output saved to: {output_path}")
    except subprocess.CalledProcessError as e:
        print(f"Error during download or conversion: {e.stderr}")
    except FileNotFoundError:
        print("Error: yt-dlp is not found. Please ensure yt-dlp is installed and accessible in your PATH.")
        print("You can install it by running: pip install yt-dlp")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

if __name__ == "__main__":
    install_package("yt-dlp") # This only installs the python package not the actual binary.
    # The user would need to have the yt-dlp binary installed.
    
    print("\n--- Video to MP3 Downloader ---")
    print("Please make sure you have the 'yt-dlp' executable installed on your system and accessible in your PATH.")
    print("You can download it from: https://github.com/yt-dlp/yt-dlp#installation")
    print("Example: If you are on Windows, you can download yt-dlp.exe and place it in a directory listed in your system's PATH, or directly in this application's folder.")
    
    while True:
        video_url = input("\nEnter video URL (or 'q' to quit): ")
        if video_url.lower() == 'q':
            break
        
        output_dir = input("Enter output directory (default: current directory): ").strip()
        if not output_dir:
            output_dir = "." # Default to current directory
        
        download_and_convert_to_mp3(video_url, output_dir)
        
    print("\nExiting. Thank you!")