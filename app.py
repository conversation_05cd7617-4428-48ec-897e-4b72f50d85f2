#!/usr/bin/env python3
"""
Music Player Pro - A comprehensive music player with playlist management and video-to-MP3 downloading.

This application provides:
- Music playback with support for multiple formats (MP3, WAV, OGG, FLAC, M4A)
- Playlist creation and management
- Video-to-MP3 downloading with yt-dlp integration
- Modern GUI interface
- Volume control and seeking
- Shuffle and repeat modes
"""

import subprocess
import sys

def install_dependencies():
    """Install required dependencies if not already installed."""
    dependencies = [
        'pygame',
        'mutagen',
        'yt-dlp',
        'Pillow'
    ]

    for package in dependencies:
        try:
            __import__(package)
            print(f"✓ '{package}' is already installed.")
        except ImportError:
            print(f"Installing '{package}'...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                print(f"✓ '{package}' installed successfully.")
            except subprocess.CalledProcessError as e:
                print(f"✗ Error installing '{package}': {e}")
                return False
    return True

def main():
    """Main entry point for the Music Player Pro application."""
    print("=" * 60)
    print("🎵 Music Player Pro - Starting Application 🎵")
    print("=" * 60)

    # Install dependencies
    print("\nChecking and installing dependencies...")
    if not install_dependencies():
        print("\n❌ Failed to install required dependencies. Please install them manually:")
        print("pip install pygame mutagen yt-dlp Pillow")
        input("\nPress Enter to exit...")
        return

    print("\n✅ All dependencies are installed!")
    print("\nStarting Music Player Pro...")

    try:
        # Import and run the music player GUI
        from music_player import main as run_music_player
        run_music_player()
    except ImportError as e:
        print(f"\n❌ Error importing music player: {e}")
        print("Make sure all required files are in the same directory.")
        input("\nPress Enter to exit...")
    except Exception as e:
        print(f"\n❌ Error starting music player: {e}")
        import traceback
        traceback.print_exc()
        input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()