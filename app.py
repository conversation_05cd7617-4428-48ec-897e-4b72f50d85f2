#!/usr/bin/env python3
"""
Music Player Pro - Web Edition

A simple, reliable web-based music player with:
- Universal browser compatibility
- Local file playback
- URL to MP3 downloading
- Clean, modern interface
- All essential playback controls
"""

import subprocess
import sys

def install_dependencies():
    """Install required dependencies if not already installed."""
    dependencies = [
        'yt-dlp'  # Only yt-dlp needed for download functionality
    ]

    for package in dependencies:
        try:
            __import__(package)
            print(f"✓ '{package}' is already installed.")
        except ImportError:
            print(f"Installing '{package}'...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                print(f"✓ '{package}' installed successfully.")
            except subprocess.CalledProcessError as e:
                print(f"✗ Error installing '{package}': {e}")
                print(f"💡 You can still use the music player without download functionality")
                return False
    return True

def main():
    """Main entry point for the Music Player Pro application."""
    print("=" * 60)
    print("🎵 Music Player Pro - Standalone Edition 🎵")
    print("=" * 60)

    print("\n🌐 Opening standalone music player...")
    print("✅ No server required - works directly in browser")
    print("🎵 All buttons function perfectly")

    try:
        import webbrowser
        import os

        # Use the standalone HTML file
        html_file = os.path.abspath("music_player_standalone.html")
        if os.path.exists(html_file):
            webbrowser.open(f"file://{html_file}")
            print(f"✅ Music player opened: {html_file}")
            print("\n🎵 How to use:")
            print("1. Click 'Select Audio Files' to add music")
            print("2. Or drag & drop audio files onto the player")
            print("3. Click ▶️ to play, all controls work!")
            print("4. Use keyboard shortcuts: Space (play/pause), arrows (next/prev)")

        else:
            print("❌ music_player_standalone.html not found!")
            print("Make sure you're in the correct directory.")

    except Exception as e:
        print(f"\n❌ Error opening music player: {e}")
        import traceback
        traceback.print_exc()

    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()