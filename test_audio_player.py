#!/usr/bin/env python3
"""
Test script for the AudioPlayer class
"""

import os
import time
from audio_player import AudioPlayer

def test_audio_player():
    """Test the AudioPlayer functionality"""
    print("=" * 60)
    print("🎵 Testing AudioPlayer Class")
    print("=" * 60)
    
    # Create audio player
    print("\n1. Creating AudioPlayer...")
    player = AudioPlayer()
    
    # Test volume
    print("\n2. Testing volume control...")
    player.set_volume(0.5)
    volume = player.get_volume()
    print(f"Volume set to: {volume}")
    
    # Look for test audio files
    print("\n3. Looking for audio files to test...")
    test_files = []
    
    # Check current directory for audio files
    for file in os.listdir('.'):
        if file.lower().endswith(('.mp3', '.wav', '.ogg')):
            test_files.append(file)
    
    if not test_files:
        print("⚠️  No audio files found in current directory")
        print("💡 To test audio playback:")
        print("   1. Add some MP3, WAV, or OGG files to this directory")
        print("   2. Run this test again")
        print("   3. Or use the main music player to add files")
        return False
    
    print(f"📁 Found {len(test_files)} audio file(s):")
    for i, file in enumerate(test_files):
        print(f"   {i+1}. {file}")
    
    # Test loading and playing first file
    test_file = test_files[0]
    print(f"\n4. Testing with file: {test_file}")
    
    # Load song
    print("   Loading song...")
    if player.load_song(test_file):
        print("   ✅ Song loaded successfully")
        
        # Get metadata
        metadata = player.get_song_metadata(test_file)
        print(f"   📝 Title: {metadata['title']}")
        print(f"   👤 Artist: {metadata['artist']}")
        print(f"   💿 Album: {metadata['album']}")
        print(f"   ⏱️  Duration: {metadata['duration']:.2f}s")
        
        # Test playback
        print("\n5. Testing playback...")
        print("   ▶️  Starting playback...")
        player.play()
        
        # Wait a bit
        print("   🎵 Playing for 3 seconds...")
        time.sleep(3)
        
        # Test pause
        print("   ⏸️  Pausing...")
        player.pause()
        time.sleep(1)
        
        # Test resume
        print("   ▶️  Resuming...")
        player.play()
        time.sleep(2)
        
        # Test stop
        print("   ⏹️  Stopping...")
        player.stop()
        
        print("\n✅ Audio player test completed!")
        print("🔊 If you heard audio during the test, the player is working correctly!")
        return True
        
    else:
        print("   ❌ Failed to load song")
        return False

def main():
    """Main test function"""
    try:
        success = test_audio_player()
        
        print("\n" + "=" * 60)
        if success:
            print("✅ AudioPlayer test PASSED!")
            print("The audio system is working correctly.")
        else:
            print("❌ AudioPlayer test FAILED!")
            print("There may be issues with audio files or system configuration.")
        
        print("\n💡 Tips:")
        print("- Make sure your system volume is up")
        print("- Check that your speakers/headphones are connected")
        print("- Try different audio file formats (MP3, WAV)")
        print("- Ensure audio files are not corrupted")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
