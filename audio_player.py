import pygame
import threading
import time
from mutagen import File
import os

class AudioPlayer:
    def __init__(self):
        pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)
        self.current_song = None
        self.is_playing = False
        self.is_paused = False
        self.position = 0
        self.duration = 0
        self.volume = 0.7
        pygame.mixer.music.set_volume(self.volume)
        self.position_thread = None
        self.stop_position_thread = False
        
    def load_song(self, file_path):
        """Load a song file"""
        try:
            self.current_song = file_path
            pygame.mixer.music.load(file_path)
            self.duration = self.get_song_duration(file_path)
            self.position = 0
            return True
        except pygame.error as e:
            print(f"Error loading song: {e}")
            return False
    
    def play(self):
        """Play the current song"""
        if self.current_song:
            if self.is_paused:
                pygame.mixer.music.unpause()
                self.is_paused = False
            else:
                pygame.mixer.music.play(start=self.position)
            self.is_playing = True
            self.start_position_tracking()
    
    def pause(self):
        """Pause the current song"""
        if self.is_playing:
            pygame.mixer.music.pause()
            self.is_paused = True
            self.is_playing = False
            self.stop_position_tracking()
    
    def stop(self):
        """Stop the current song"""
        pygame.mixer.music.stop()
        self.is_playing = False
        self.is_paused = False
        self.position = 0
        self.stop_position_tracking()
    
    def set_volume(self, volume):
        """Set volume (0.0 to 1.0)"""
        self.volume = max(0.0, min(1.0, volume))
        pygame.mixer.music.set_volume(self.volume)
    
    def get_volume(self):
        """Get current volume"""
        return self.volume
    
    def set_position(self, position):
        """Set playback position (in seconds)"""
        if self.current_song and 0 <= position <= self.duration:
            self.position = position
            if self.is_playing or self.is_paused:
                pygame.mixer.music.stop()
                pygame.mixer.music.play(start=position)
                if self.is_paused:
                    pygame.mixer.music.pause()
    
    def get_position(self):
        """Get current playback position"""
        return self.position
    
    def get_duration(self):
        """Get song duration"""
        return self.duration
    
    def is_song_playing(self):
        """Check if a song is currently playing"""
        return self.is_playing and pygame.mixer.music.get_busy()
    
    def get_song_duration(self, file_path):
        """Get duration of a song file"""
        try:
            audio_file = File(file_path)
            if audio_file is not None and hasattr(audio_file, 'info'):
                return audio_file.info.length
            return 0
        except:
            return 0
    
    def get_song_metadata(self, file_path):
        """Get metadata of a song file"""
        try:
            audio_file = File(file_path)
            if audio_file is not None:
                title = audio_file.get('TIT2', [os.path.basename(file_path)])[0] if audio_file.get('TIT2') else os.path.basename(file_path)
                artist = audio_file.get('TPE1', ['Unknown Artist'])[0] if audio_file.get('TPE1') else 'Unknown Artist'
                album = audio_file.get('TALB', ['Unknown Album'])[0] if audio_file.get('TALB') else 'Unknown Album'
                duration = audio_file.info.length if hasattr(audio_file, 'info') else 0
                
                return {
                    'title': str(title),
                    'artist': str(artist),
                    'album': str(album),
                    'duration': duration,
                    'file_path': file_path
                }
        except:
            pass
        
        return {
            'title': os.path.basename(file_path),
            'artist': 'Unknown Artist',
            'album': 'Unknown Album',
            'duration': 0,
            'file_path': file_path
        }
    
    def start_position_tracking(self):
        """Start tracking playback position"""
        self.stop_position_thread = False
        if self.position_thread is None or not self.position_thread.is_alive():
            self.position_thread = threading.Thread(target=self._track_position)
            self.position_thread.daemon = True
            self.position_thread.start()
    
    def stop_position_tracking(self):
        """Stop tracking playback position"""
        self.stop_position_thread = True
    
    def _track_position(self):
        """Track playback position in a separate thread"""
        start_time = time.time()
        start_position = self.position
        
        while not self.stop_position_thread and self.is_playing:
            if pygame.mixer.music.get_busy():
                elapsed = time.time() - start_time
                self.position = start_position + elapsed
                if self.position >= self.duration:
                    self.position = self.duration
                    self.is_playing = False
                    break
            else:
                self.is_playing = False
                break
            time.sleep(0.1)
