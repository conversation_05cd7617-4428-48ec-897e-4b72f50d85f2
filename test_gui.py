#!/usr/bin/env python3
"""
Simple test to check if tkinter GUI can be created
"""

import tkinter as tk
from tkinter import ttk
import sys

def test_gui():
    """Test basic GUI functionality"""
    try:
        print("Testing tkinter GUI...")
        
        # Create root window
        root = tk.Tk()
        root.title("GUI Test")
        root.geometry("300x200")
        
        # Add a simple label
        label = ttk.Label(root, text="GUI Test Successful!")
        label.pack(pady=20)
        
        # Add a button to close
        def close_app():
            print("Closing GUI test...")
            root.destroy()
        
        button = ttk.Button(root, text="Close", command=close_app)
        button.pack(pady=10)
        
        print("GUI created successfully. Starting mainloop...")
        
        # Start the GUI (this will block until window is closed)
        root.mainloop()
        
        print("GUI test completed successfully!")
        return True
        
    except Exception as e:
        print(f"GUI test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 40)
    print("🖥️  GUI Test")
    print("=" * 40)
    
    if test_gui():
        print("✅ GUI test passed!")
    else:
        print("❌ GUI test failed!")
        print("This might indicate a display issue.")
