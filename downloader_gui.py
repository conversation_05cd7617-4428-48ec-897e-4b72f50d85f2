import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import subprocess
import os
import sys

class DownloaderGUI:
    def __init__(self, parent):
        self.parent = parent
        self.window = None
        
    def show_downloader(self):
        """Show the downloader window"""
        if self.window is None or not self.window.winfo_exists():
            self.create_downloader_window()
        else:
            self.window.lift()
            self.window.focus()
    
    def create_downloader_window(self):
        """Create the downloader window"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("Video to MP3 Downloader")
        self.window.geometry("600x400")
        self.window.resizable(True, True)
        
        # Main frame
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.window.columnconfigure(0, weight=1)
        self.window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # URL input
        ttk.Label(main_frame, text="Video URL:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.url_var = tk.StringVar()
        url_entry = ttk.Entry(main_frame, textvariable=self.url_var, width=50)
        url_entry.grid(row=0, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 5))
        
        # Output directory
        ttk.Label(main_frame, text="Output Directory:").grid(row=1, column=0, sticky=tk.W, pady=(0, 5))
        self.output_var = tk.StringVar(value=os.getcwd())
        output_entry = ttk.Entry(main_frame, textvariable=self.output_var, width=40)
        output_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=(0, 5))
        
        browse_btn = ttk.Button(main_frame, text="Browse", command=self.browse_output_dir)
        browse_btn.grid(row=1, column=2, sticky=tk.W, padx=(5, 0), pady=(0, 5))
        
        # Quality selection
        ttk.Label(main_frame, text="Audio Quality:").grid(row=2, column=0, sticky=tk.W, pady=(0, 5))
        self.quality_var = tk.StringVar(value="best")
        quality_combo = ttk.Combobox(main_frame, textvariable=self.quality_var, 
                                   values=["best", "320k", "256k", "192k", "128k"], 
                                   state="readonly", width=15)
        quality_combo.grid(row=2, column=1, sticky=tk.W, pady=(0, 5))
        
        # Download button
        self.download_btn = ttk.Button(main_frame, text="Download", command=self.start_download)
        self.download_btn.grid(row=3, column=0, columnspan=3, pady=10)
        
        # Progress bar
        self.progress_var = tk.StringVar(value="Ready to download")
        ttk.Label(main_frame, textvariable=self.progress_var).grid(row=4, column=0, columnspan=3, pady=(0, 5))
        
        self.progress_bar = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress_bar.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Output text area
        ttk.Label(main_frame, text="Output:").grid(row=6, column=0, sticky=tk.W)
        
        # Text widget with scrollbar
        text_frame = ttk.Frame(main_frame)
        text_frame.grid(row=7, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(5, 0))
        text_frame.columnconfigure(0, weight=1)
        text_frame.rowconfigure(0, weight=1)
        
        self.output_text = tk.Text(text_frame, height=10, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.output_text.yview)
        self.output_text.configure(yscrollcommand=scrollbar.set)
        
        self.output_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # Configure grid weights for resizing
        main_frame.rowconfigure(7, weight=1)
        
        # Clear and close buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=8, column=0, columnspan=3, pady=(10, 0))
        
        ttk.Button(button_frame, text="Clear Output", command=self.clear_output).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="Close", command=self.window.destroy).pack(side=tk.RIGHT)
    
    def browse_output_dir(self):
        """Browse for output directory"""
        directory = filedialog.askdirectory(initialdir=self.output_var.get())
        if directory:
            self.output_var.set(directory)
    
    def start_download(self):
        """Start the download process in a separate thread"""
        url = self.url_var.get().strip()
        if not url:
            messagebox.showerror("Error", "Please enter a video URL")
            return
        
        output_dir = self.output_var.get().strip()
        if not output_dir:
            output_dir = os.getcwd()
        
        # Disable download button and start progress
        self.download_btn.config(state='disabled')
        self.progress_bar.start()
        self.progress_var.set("Downloading...")
        
        # Start download in separate thread
        thread = threading.Thread(target=self.download_video, args=(url, output_dir))
        thread.daemon = True
        thread.start()
    
    def download_video(self, url, output_dir):
        """Download and convert video to MP3"""
        try:
            self.log_output(f"Starting download: {url}\n")
            self.log_output(f"Output directory: {output_dir}\n")
            
            # Ensure output directory exists
            os.makedirs(output_dir, exist_ok=True)
            
            # Determine quality setting
            quality = self.quality_var.get()
            if quality == "best":
                quality_arg = "0"
            else:
                quality_arg = quality
            
            # Build yt-dlp command
            yt_dlp_path = self.find_yt_dlp()
            if not yt_dlp_path:
                self.log_output("Error: yt-dlp not found. Please install yt-dlp.\n")
                return
            
            command = [
                yt_dlp_path,
                "-x",  # Extract audio
                "--audio-format", "mp3",
                "--audio-quality", quality_arg,
                "-o", os.path.join(output_dir, "%(title)s.%(ext)s"),
                url
            ]
            
            # Execute command
            process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                universal_newlines=True
            )
            
            # Read output in real-time
            for line in process.stdout:
                self.log_output(line)
            
            process.wait()
            
            if process.returncode == 0:
                self.log_output("\n✓ Download completed successfully!\n")
                self.progress_var.set("Download completed!")
            else:
                self.log_output(f"\n✗ Download failed with return code: {process.returncode}\n")
                self.progress_var.set("Download failed!")
                
        except Exception as e:
            self.log_output(f"\nError: {str(e)}\n")
            self.progress_var.set("Error occurred!")
        
        finally:
            # Re-enable download button and stop progress
            self.window.after(0, self.download_finished)
    
    def download_finished(self):
        """Called when download is finished"""
        self.download_btn.config(state='normal')
        self.progress_bar.stop()
    
    def find_yt_dlp(self):
        """Find yt-dlp executable"""
        # Check current directory first
        local_paths = ["yt-dlp.exe", "yt-dlp_x86.exe", "yt-dlp"]
        for path in local_paths:
            if os.path.exists(path):
                return path
        
        # Check if yt-dlp is in PATH
        try:
            subprocess.run(["yt-dlp", "--version"], capture_output=True, check=True)
            return "yt-dlp"
        except (subprocess.CalledProcessError, FileNotFoundError):
            pass
        
        return None
    
    def log_output(self, text):
        """Add text to output area"""
        def update_text():
            self.output_text.insert(tk.END, text)
            self.output_text.see(tk.END)
        
        if self.window and self.window.winfo_exists():
            self.window.after(0, update_text)
    
    def clear_output(self):
        """Clear the output text area"""
        self.output_text.delete(1.0, tk.END)
