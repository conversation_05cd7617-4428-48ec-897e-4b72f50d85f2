#!/usr/bin/env python3
"""
Test script to verify WebM support in the music player.
This script tests if py<PERSON> can load and play WebM files.
"""

import pygame
import sys
import os

def test_webm_support():
    """Test WebM file support"""
    print("Testing WebM support in pygame...")
    
    try:
        # Initialize pygame mixer
        pygame.mixer.init()
        print("✓ Pygame mixer initialized successfully")
        
        # Test if we can create a basic WebM file path (not actually loading)
        test_webm_path = "test.webm"
        
        # Check supported formats
        print("\nSupported formats by pygame:")
        print("- MP3: Supported")
        print("- WAV: Supported") 
        print("- OGG: Supported")
        print("- WebM: Limited support (depends on audio codec)")
        
        print("\nWebM Support Notes:")
        print("- WebM files with Vorbis audio: Usually supported")
        print("- WebM files with Opus audio: May not be supported")
        print("- WebM files with VP8/VP9 video: Video ignored, audio extracted if supported")
        
        print("\nTo test with actual WebM files:")
        print("1. Add WebM files to your music library")
        print("2. Try playing them in the music player")
        print("3. Check the console for any error messages")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing WebM support: {e}")
        return False
    
    finally:
        pygame.mixer.quit()

def main():
    """Main test function"""
    print("=" * 50)
    print("🎵 Music Player Pro - WebM Support Test 🎵")
    print("=" * 50)
    
    if test_webm_support():
        print("\n✅ WebM support test completed successfully!")
        print("\nNote: Actual WebM playback depends on the specific")
        print("audio codec used in the WebM file.")
    else:
        print("\n❌ WebM support test failed!")
    
    print("\nPress Enter to exit...")
    input()

if __name__ == "__main__":
    main()
